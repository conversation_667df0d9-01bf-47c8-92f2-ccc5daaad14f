import { useTranslation } from '@/app/i18n/client'
import Pie<PERSON>hartWithList from '@/components/EbPieChart/NewPieChartWithList'
import { ApplicationPropsContext } from '@/context/application-props'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import React from 'react'

ShareClassesChart.fragment = graphql(`
  fragment ShareClassesChart on ShareholderRegisterCapital {
    issuedShares {
      formatted
      value
      compact
    }
    shareClasses {
      prefix
      name
      issuedShares {
        value
        formatted
        compact
      }
    }
  }
`)

export default function ShareClassesChart({ data }: { data: FragmentOf<typeof ShareClassesChart.fragment> }) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const { issuedShares, shareClasses } = readFragment(ShareClassesChart.fragment, data)

  const pieData = shareClasses.map((shareClass) => ({
    label: `${shareClass.name} (${shareClass.prefix})`,
    value: shareClass.issuedShares.value,
    displayedValue: (shareClass.issuedShares.value / issuedShares.value) * 100 + '%',
  }))

  return (
    <PieChartWithList
      data={pieData}
      width={240}
      height={240}
      showAvatar
      showTotal
      customTitle={t('total-shares')}
      customValue={issuedShares.compact}
    />
  )
}
