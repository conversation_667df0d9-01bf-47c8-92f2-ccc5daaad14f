import { BellIcon } from '@/components/EbanaIcons'
import { CompanyContext } from '@/context/company'
import { graphql } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Button, Flex } from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { route } from 'nextjs-routes'
import React from 'react'
import { IoClose } from 'react-icons/io5'
import { useMutation, useQuery } from 'urql'


export enum Insight {
  COMPANY_PROFILE = 'COMPANY_PROFILE',
  BOARD_MEETING = 'BOARD_MEETING',
  ASSEMBLY_MEETING = 'ASSEMBLY_MEETING',
  COMMITTEE_MEETING = 'COMMITTEE_MEETING',
  INCENTIVE_PROGRAM = 'INCENTIVE_PROGRAM',
  PRESS_RELEASE = 'PRESS_RELEASE',
  GRANT_PRIVILEGES = 'GRANT_PRIVILEGES',
}

const query = graphql(`
  query BannerQuery($id: ID!) {
    company(id: $id) {
      smartInsight {
        insight
        dismissForm {
          available
          defaultValues
        }
      }
    }
  }
`)

export function Banner() {
  const { t, lng } = useEbanaLocale()

  const { id } = React.useContext(CompanyContext)

  const router = useRouter()

  const [showBanner, setShowBanner] = React.useState(true)

  const [_, dismissInsight] = useMutation(
    graphql(`
      mutation BannerMutation($id: ID!, $input: CompanyDismissInsightInput!) {
        companyDismissInsight(id: $id, input: $input) {
          company {
            id
          }
        }
      }
    `)
  )

  const [{ data }] = useQuery({ query: query, variables: { id } })

  console.log(data)

  async function onDismiss() {
    await dismissInsight({
      id,
      input: {
        insight: data.company.smartInsight.insight,
      },
    })
    setShowBanner(false)
  }

  return (
    data &&
    data.company.smartInsight.insight === Insight.COMPANY_PROFILE &&
    showBanner && (
      <Flex
        justifyContent='space-between'
        alignItems={{ base: 'flex-start', md: 'center' }}
        marginTop={{ base: 0, md: '-2.25rem' }}
        bg='#5858FF'
        py='1rem'
        px={{ base: '1rem', md: '3rem' }}
        mb={{ base: 0, md: '2rem' }}>
        <Flex alignItems='center' gap={2} flexWrap='wrap'>
          <BellIcon />
          <Flex flexDir={{ base: 'column', md: 'row' }} gap={2}>
            <Box fontWeight='medium' color='#fff'>
              {t('insight_complete_company_profile')}
            </Box>

            <Box
              my='auto'
              width='0.1875rem'
              height='0.1875rem'
              borderRadius='full'
              bg='#fff'
              display={{ base: 'none', md: 'unset' }}
            />

            <Box fontWeight='lighter' color='#fff' textStyle='body4'>
              {t('insight_complete_company_profile_details')}
            </Box>

            <Box
              onClick={() => {
                router.push(
                  route({
                    pathname: '/[lng]/company/[companyId]/profile/edit',
                    query: { lng, companyId: id },
                    hash: 'estimatedValue',
                  })
                )
              }}
              fontWeight='medium'
              color='#fff'
              textDecoration='underline'
              textUnderlineOffset={4}
              cursor='pointer'>
              {t('get_started')}
            </Box>
          </Flex>
        </Flex>

        <Button variant='plain' onClick={() => onDismiss()}>
          <IoClose fill='#fff' stroke='white' />
        </Button>
      </Flex>
    )
  )
}
