import {
  CompanyBoardIcon,
  CompanyCaptableIcon,
  CompanyCommitteeIcon,
  CompanyLogoIcon,
  CompanyPlanIcon,
  CompanyStampIcon,
} from '@/components/EbanaIcons'
import { CompanyContext } from '@/context/company'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { ResultOf } from 'gql.tada'
import { route } from 'nextjs-routes'
import React from 'react'
import OnboardingCards from './OnboardingCards'

export function GetOnboardingCards({ onboarding }: { onboarding: ResultOf<typeof OnboardingCards.fragment> }) {
  const { t, lng } = useEbanaLocale()
  const { id: companyId } = React.useContext(CompanyContext)

  return [
    {
      view: onboarding.capTable,
      id: 'capTable',
      title: t('setup.captable.title'),
      description: t('setup.captable.description'),
      actionLabel: t('setup.captable.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/captable/setup',
        query: { lng, companyId },
      }),
      icon: <CompanyCaptableIcon />,
    },
    {
      view: onboarding.logo,
      id: 'logo',
      title: t('setup.logo.title'),
      description: t('setup.logo.description'),
      actionLabel: t('setup.logo.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/profile/edit',
        query: { lng, companyId },
      }),
      icon: <CompanyLogoIcon />,
    },
    {
      view: onboarding.stamp,
      id: 'stamp',
      title: t('setup.stamp.title'),
      description: t('setup.stamp.description'),
      actionLabel: t('setup.stamp.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/profile/edit',
        query: { lng, companyId },
      }),
      icon: <CompanyStampIcon />,
    },
    {
      view: onboarding.board,
      id: 'board',
      title: t('setup.board.title'),
      description: t('setup.board.description'),
      actionLabel: t('setup.board.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/board/setup',
        query: { lng, companyId },
      }),
      icon: <CompanyBoardIcon />,
    },
    {
      view: onboarding.committees,
      id: 'committees',
      title: t('setup.committee.title'),
      description: t('setup.committee.description'),
      actionLabel: t('setup.committee.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/committees/setup',
        query: { lng, companyId },
      }),
      icon: <CompanyCommitteeIcon />,
    },
    {
      view: onboarding.incentives,
      id: 'incentives',
      title: t('setup.plan.title'),
      description: t('setup.plan.description'),
      actionLabel: t('setup.plan.actionLabel'),
      link: route({
        pathname: '/[lng]/company/[companyId]/equity-incentive-grants/management',
        query: { lng, companyId },
      }),
      icon: <CompanyPlanIcon />,
    },
  ]
}
