import { SquarePageIcon } from '@/components/EbanaIcons'
import NeedlePie from '@/components/EbPieChart/NeedlePie'
import { ProgressBar } from '@/components/ui/progress'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, ProgressRoot, Stack } from '@chakra-ui/react'
import ResponsiveCard from '../../../../../../../../components/ResponsiveCard'

GovernanceDashboard.fragment = graphql(`
  fragment GovernanceDashboard on CompanyGovernanceSummary {
    yearBoardMeetings {
      ...Number
    }
    yearCommitteeMeetings {
      ...Number
    }
    yearAssemblyMeetings {
      ...Number
    }
  }

  fragment Number on NumberToTotal {
    n {
      value
      formatted
    }
    t {
      value
      formatted
    }
    p {
      value
      formatted
    }
  }
`)

export default function GovernanceDashboard({ data }: { data: FragmentOf<typeof GovernanceDashboard.fragment> }) {
  const { t, lng } = useEbanaLocale()

  const { yearAssemblyMeetings, yearBoardMeetings, yearCommitteeMeetings } = readFragment(
    GovernanceDashboard.fragment,
    data
  )

  const heldMeetings = [
    {
      title: 'board',
      n: yearBoardMeetings.n,
      t: yearBoardMeetings.t,
      p: yearBoardMeetings.p,
      color: '#6490EB',
    },
    {
      title: 'committees',
      n: yearCommitteeMeetings.n,
      t: yearCommitteeMeetings.t,
      p: yearCommitteeMeetings.p,
      color: 'linear-gradient(90deg, #6490EB 0%, #5858FF 50%)',
    },
    {
      title: 'assemblies',
      n: yearAssemblyMeetings.n,
      t: yearAssemblyMeetings.t,
      p: yearAssemblyMeetings.p,
      color: '#5858FF',
    },
  ]

  const NeedlePieTotal = yearAssemblyMeetings.t.value + yearBoardMeetings.t.value + yearCommitteeMeetings.t.value
  const NeedlePieValue =
    Math.min(yearAssemblyMeetings.n.value, yearAssemblyMeetings.t.value) +
    Math.min(yearBoardMeetings.n.value, yearBoardMeetings.t.value) +
    Math.min(yearCommitteeMeetings.n.value, yearCommitteeMeetings.t.value)

  return (
    <ResponsiveCard
      headerContent={
        <Box>
          <Box>{t('governance_summary')}</Box>
        </Box>
      }
      mainContent={
        <Stack>
          <Flex gap='1rem' flexWrap='wrap'>
            <Stack w='fit-content' minW='fit-content' borderRadius='10px' borderWidth='1px' p='20px' flexGrow={1}>
              <Box textStyle='body3' fontWeight={600}>
                {t('compliance_score')}
              </Box>
              <Box mx='auto' mt='-6px'>
                <NeedlePie total={NeedlePieTotal} value={NeedlePieValue} title={t('compliance_score')} />
              </Box>
            </Stack>
            <Stack borderRadius='10px' px='20px' py='18px' gap='16px' borderWidth='1px' w='14rem' flexGrow={1}>
              <Box textStyle='body3' fontWeight={600} w='fit-content'>
                {t('total_meetings_held')}
              </Box>
              <Stack gap='16px' w='100%' mt='auto'>
                {heldMeetings.map((meeting, index) => {
                  return (
                    <Flex key={index} justifyContent='space-between'>
                      <Flex gap='10px'>
                        <SquarePageIcon />
                        <Box textStyle='body4' fontWeight={500}>
                          {t(meeting.title)}
                        </Box>
                      </Flex>
                      <Box textStyle='body4' fontWeight={600}>
                        {meeting.n.formatted}
                      </Box>
                    </Flex>
                  )
                })}
              </Stack>
            </Stack>
          </Flex>
          <Stack w='100%' minW='fit-content' borderRadius='10px' pt='1rem' flexGrow={1}>
            <Box textStyle='body3' fontWeight={600}>
              {t('required_meetings')}
            </Box>
            <Flex w='100%' gap='1rem' dir='ltr' flexWrap='wrap'>
              {heldMeetings.map((meeting, index) => {
                return (
                  <Stack
                    key={index}
                    borderRadius='10px'
                    borderWidth='1px'
                    px='14px'
                    py='12px'
                    w='calc( 33.3% - 0.75rem )'
                    minW='10.95rem'
                    gap={0}
                    dir={lng === 'en' ? 'ltr' : 'rtl'}>
                    <Flex justifyContent='space-between'>
                      <Box fontWeight={600} textStyle='body4'>
                        {t(meeting.title)}
                      </Box>
                      <Box
                        fontWeight={600}
                        textStyle='body4'
                        borderRadius='50px'
                        px='8px'
                        bg={meeting.color}
                        color='white'>
                        {meeting.p.formatted}
                      </Box>
                    </Flex>
                    <Flex alignContent='center'>
                      <Box textStyle='h3' fontWeight={600}>
                        {meeting.n.formatted}
                      </Box>
                      <Box
                        height='fit-content'
                        alignSelf='center'
                        mt='4px'
                        textStyle='body4'
                        fontWeight={500}
                        color='#8A94A6'>
                        / {t('of')} {meeting.t.formatted}
                      </Box>
                    </Flex>
                    <ProgressRoot mt='14px' value={meeting.p.value * 100} variant='purple' shape='rounded'>
                      <ProgressBar dir='ltr' bg={meeting.color} />
                    </ProgressRoot>
                  </Stack>
                )
              })}
            </Flex>
          </Stack>
        </Stack>
      }
    />
  )
}
