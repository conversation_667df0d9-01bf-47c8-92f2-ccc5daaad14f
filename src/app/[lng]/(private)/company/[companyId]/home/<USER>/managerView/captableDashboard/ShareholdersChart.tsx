import { useTranslation } from '@/app/i18n/client'
import PieChartWithList from '@/components/EbPieChart/NewPieChartWithList'
import { ApplicationPropsContext } from '@/context/application-props'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import React from 'react'

ShareholdersChart.fragment = graphql(`
  fragment ShareholdersChart on ShareholderRegisterTop5 {
    shareholders {
      name
      totalShares {
        value
        formatted
      }
      totalOwnership {
        value
        formatted
      }
    }
    ownership {
      value
      formatted
      complement
      complementFormatted
    }
  }
`)

export default function ShareholdersChart({
  data,
  totalShareholders,
}: {
  data: FragmentOf<typeof ShareholdersChart.fragment>
  totalShareholders: { value: number; formatted: string; compact: string }
}) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const { shareholders, ownership } = readFragment(ShareholdersChart.fragment, data)

  const pieData = [
    ...shareholders.map((shareholder) => ({
      label: shareholder.name,
      value: shareholder.totalShares.value,
      displayedValue: shareholder.totalOwnership.formatted,
    })),
    totalShareholders.value > 5
      ? {
          label: t('others'),
          value: ownership.complement,
          displayedValue: ownership.complementFormatted,
        }
      : null,
  ].filter(Boolean)

  return (
    <PieChartWithList
      data={pieData}
      width={240}
      height={240}
      showAvatar
      showTotal
      customTitle={t('total_shareholders')}
      customValue={totalShareholders.compact}
    />
  )
}
