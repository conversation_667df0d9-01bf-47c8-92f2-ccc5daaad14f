import Calendar from '@/components/Calendar/Calendar'
import { ErrorBoundary } from '@/components/ErrorBoundary'
import EstimatedValue from '@/components/EstimatedValueDashboard'
import { GqlData } from '@/components/GqlData'
import Header from '@/components/header'
import HomeSkeleton from '@/components/HomeSkeleton'
import NavigationContainer from '@/components/Navigation'
import ResponsiveContainer from '@/components/ResponsiveContainer'
import { UnitErrorBox } from '@/components/UnitErrorBox'
import { CompanyContext } from '@/context/company'
import UpcomingEvents from '@/features/dashboard/upcoming-events'
import { KeyServices } from '@/features/KeyServices'
import { graphql } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Stack } from '@chakra-ui/react'
import React from 'react'
import { useQuery } from 'urql'
import { Banner } from './Banner'
import CaptableDashboard from './captableDashboard/CaptableDashboard'
import EIPDashboard from './EIPDashboard'
import GovernanceDashboard from './GovernanceDashboard'
import OnboardingCards from './onboarding/OnboardingCards'
import { OnboardingProgress } from './onboarding/OnboardingProgress'
import { QuickActionsSection } from './QuickActions'

const HomePageQuery = graphql(
  `
    query HomePageQuery($id: ID!) {
      company(id: $id) {
        ...QuickActionsSection
        equityIncentiveReports {
          totalShareCount {
            value
          }
          ...EquityIncentivesDashboard
        }
        register {
          ...CaptableCharts
          ...EstimatedValue
        }
        onboarding {
          ...OnboardingProgress
        }
        calendar {
          ...Calendar
        }
        upcomingMeetings {
          ...UpcomingEvent
        }
        governanceSummary {
          ...GovernanceDashboard
        }
      }
    }
  `,
  [
    GovernanceDashboard.fragment,
    QuickActionsSection.fragment,
    EstimatedValue.registerFragment,
    OnboardingProgress.fragment,
    CaptableDashboard.fragment,
    EIPDashboard.fragment,
    Calendar.fragment,
    UpcomingEvents.fragment,
  ]
)

export function ManagerView() {
  const { t, lng } = useEbanaLocale()

  const { id, tradeName } = React.useContext(CompanyContext)

  const ref = React.useRef(null)

  React.useEffect(() => {
    const timeout = setTimeout(() => {
      if (ref.current) {
        ref.current.style.borderRight = 'none'
      }
    }, 2600)

    return () => clearTimeout(timeout)
  }, [])

  const response = useQuery({ query: HomePageQuery, variables: { id } })

  const [{ data }] = response

  return (
    <NavigationContainer
      pageVariant='normal'
      header={
        <Header
          title={
            <Stack gap={0} px='1rem'>
              <Box
                textStyle='body4'
                fontWeight={600}
                style={{ overflow: 'hidden', whiteSpace: 'nowrap', display: 'inline-block' }}>
                {t('welcome_to')}
              </Box>
              <Box
                ref={ref}
                className='typewriter'
                textStyle='h1'
                lineHeight='short'
                fontWeight={700}
                maxLines={1}
                mt='-0.2rem'>
                {t('company_workspace', {
                  tradeName: tradeName,
                })}
              </Box>
            </Stack>
          }
        />
      }>
      <Banner />
      <GqlData
        response={response}
        render={() => {
          return (
            <ResponsiveContainer
              topContent={<QuickActionsSection data={data.company} />}
              mainContent={
                <>
                  <OnboardingCards data={data.company.onboarding} />

                  {data.company.register && (
                    <>
                      <ErrorBoundary fallback={<UnitErrorBox title={t('cap_table_summary')} />}>
                        <CaptableDashboard data={data.company.register} />
                      </ErrorBoundary>

                      <ErrorBoundary fallback={<UnitErrorBox title={t('empty_state')} />}>
                        <EstimatedValue registerData={data.company.register} />
                      </ErrorBoundary>
                    </>
                  )}
                  <OnboardingCards data={data.company.onboarding} />

                  {!!data.company.equityIncentiveReports.totalShareCount.value && (
                    <ErrorBoundary fallback={<UnitErrorBox title={t('empty_state')} />}>
                      <EIPDashboard data={data.company.equityIncentiveReports} />
                    </ErrorBoundary>
                  )}

                  <ErrorBoundary fallback={<UnitErrorBox title={t('governance_summary')} />}>
                    <GovernanceDashboard data={data.company.governanceSummary} />
                  </ErrorBoundary>

                  <Box>
                    <KeyServices />
                  </Box>
                </>
              }
              sideContent={
                <>
                  <ErrorBoundary fallback={<UnitErrorBox title={t('calendaer')} />}>
                    <Calendar data={data.company.calendar} />
                  </ErrorBoundary>
                  {data.company.upcomingMeetings != null &&
                    Array.isArray(data.company.upcomingMeetings) &&
                    data.company.upcomingMeetings.length > 0 && (
                      <ErrorBoundary fallback={<UnitErrorBox title={t('upcoming_events')} />}>
                        <UpcomingEvents data={data.company.upcomingMeetings} />
                      </ErrorBoundary>
                    )}
                  <ErrorBoundary fallback={<UnitErrorBox title={t('empty_state')} />}>
                    <Box p='1rem'>
                      <OnboardingProgress data={data.company.onboarding} />
                    </Box>
                  </ErrorBoundary>
                </>
              }
            />
          )
        }}
        renderLoading={<HomeSkeleton />}
      />
    </NavigationContainer>
  )
}
