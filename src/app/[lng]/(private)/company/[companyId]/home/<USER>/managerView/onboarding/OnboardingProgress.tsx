import { useTranslation } from '@/app/i18n/client'
import { CheckCircleIcon } from '@/components/EbanaIcons'
import Modal from '@/components/modal'
import { ApplicationPropsContext } from '@/context/application-props'
import { FragmentOf, graphql, readFragment, ResultOf } from '@/graphql'
import { Box, Flex, Heading, HStack, Link, Progress, Separator, Stack, Text } from '@chakra-ui/react'
import React, { useRef, useState } from 'react'
import { FaChevronDown, FaChevronUp } from 'react-icons/fa'
import { IoMdClose } from 'react-icons/io'
import { LuChevronRight } from 'react-icons/lu'
import { GetOnboardingCards } from './GetOnboardingCards'
import { ProgressMessage } from './ProgressMessage'

OnboardingProgress.fragment = graphql(`
  fragment OnboardingProgress on CompanyOnboarding {
    logo
    stamp
    board
    capTable
    committees
    incentives
  }
`)

function OnboardingModal({
  percentComplete,
  onboarding,
  setOpen,
  open,
}: {
  percentComplete: number
  onboarding: ResultOf<typeof OnboardingProgress.fragment>
  setOpen: (open: boolean) => void
  open: boolean
}) {
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const cards = GetOnboardingCards({ onboarding: onboarding })

  const headerRef = useRef<HTMLDivElement>(null)
  const body = (
    <Box
      position={{ base: 'fixed', md: 'static' }}
      bottom='0'
      left='0'
      right='0'
      zIndex='modal'
      bg={{ base: 'white' }}
      borderTopRadius='2xl'
      boxShadow='lg'
      w='100%'
      p='0'>
      <Box p='0' position='relative' overflow='visible'>
        <Box overflow='hidden' position='relative' ref={headerRef} bg='#131A2A' color='white' borderTopRadius='lg'>
          <Stack p='4'>
            <Heading as='h4' size='md' color='white' display='flex' alignItems='center' justifyContent='space-between'>
              {t('onboarding.checklist')}
              <Box display={{ base: 'none', md: 'block' }}>
                <IoMdClose cursor='pointer' color='white' onClick={() => setOpen(false)} />
              </Box>

              <Box display={{ base: 'block', md: 'none' }}>
                <FaChevronDown cursor='pointer' color='white' onClick={() => setOpen(false)} />
              </Box>
            </Heading>
          </Stack>
          <Separator orientation='horizontal' borderColor='#4E5D78' />
          <Box p='4'>
            <HStack mb='2' justifyContent='space-between'>
              <ProgressMessage numberComplete={Object.values(onboarding).filter(Boolean).length} />
              <Text fontWeight='bold' fontSize='sm'>
                {percentComplete}%
              </Text>
            </HStack>

            <Progress.Root value={percentComplete} w='100%' mb='2' border='none'>
              <Progress.Track>
                <Progress.Range bg='primary.500' transition='width 1s ease-in-out' />
              </Progress.Track>
            </Progress.Root>
            <Text fontSize='sm' fontWeight='medium' color='whiteAlpha.800'>
              {t('onboarding.step')} {Object.values(onboarding).filter(Boolean).length}/{Object.keys(onboarding).length}
            </Text>
          </Box>
        </Box>

        <Stack gap={4} p='4' overflowY='auto' height='fit-content' maxH={{ base: 'auto', md: 'calc(85vh - 200px)' }}>
          {cards.map((card: any) => {
            const isCompleted = onboarding[card.id]
            return (
              <Link
                key={card.id}
                href={!isCompleted ? card.link : undefined}
                cursor={isCompleted ? 'default' : 'pointer'}
                _hover={{ textDecoration: 'none' }}
                _focus={{ boxShadow: 'none', outline: 'none' }}>
                <Flex
                  px={4}
                  py={2}
                  borderRadius='1rem'
                  borderWidth='1px'
                  borderColor='#DADDE4'
                  align='center'
                  gap={4}
                  w='100%'
                  bg='white'
                  _hover={{ boxShadow: isCompleted ? 'none' : 'sm' }}>
                  <Box
                    bg='gray.50'
                    borderRadius='full'
                    p={2}
                    display='flex'
                    alignItems='center'
                    justifyContent='center'
                    boxSize='56px'
                    flexShrink={0}
                    filter={isCompleted ? 'grayscale(100%)' : 'none'}>
                    {card.icon}
                  </Box>
                  <Box flex='1'>
                    <Heading color={isCompleted ? 'primary.500' : '#00263A'} as='h5' size='md' mb={1}>
                      {card.title}
                    </Heading>
                    <Text fontSize='0.6rem;' color='#4E5D78'>
                      {card.description}
                    </Text>
                  </Box>
                  <Box pl='2'>
                    {isCompleted ? (
                      <CheckCircleIcon />
                    ) : (
                      <Box transform={lng === 'ar' ? 'scaleX(-1)' : 'none'}>
                        <LuChevronRight fontWeight='400' size={26} color='#00263A' />
                      </Box>
                    )}
                  </Box>
                </Flex>
              </Link>
            )
          })}
        </Stack>
        <Box position='absolute' top='100%' mt='8' width='100%' display='flex' justifyContent='center'>
          <Flex
            display={{ base: 'none', md: 'flex' }}
            bg='white'
            transform={{ base: 'translateX(-50%)', md: 'none' }}
            px='1.5rem'
            py='0.75rem'
            borderRadius='full'
            align='center'
            gap='1rem'
            fontWeight='bold'
            fontSize='md'
            w='max-content'
            justifyContent='space-between'>
            <Text>{t('onboarding.onboarding')} </Text>
            <Separator orientation='vertical' h='1.5rem' />
            <Text>
              {percentComplete}% {t('onboarding.complete')}
            </Text>
          </Flex>
        </Box>
      </Box>
    </Box>
  )

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      Body={body}
      size='md'
      right={{ base: 'unset', md: lng === 'en' ? '5vh' : 'unset' }}
      left={{ base: 'unset', md: lng === 'ar' ? '5vh' : 'unset' }}
      top={{ base: 'auto', md: '3vh' }}
      bottom={{ base: '0', md: 'auto' }}
      position={{ base: 'fixed', md: 'static' }}
      h={{ base: 'max-content', md: '100%' }}
      style={{
        overflow: 'visible',
        marginInline: 'unset',
        maxWidth: '30rem',
        position: 'fixed',
        padding: '0',
        height: 'max-content',
        background: 'transparent',
      }}
    />
  )
}

export function OnboardingProgress({ data }: { data: FragmentOf<typeof OnboardingProgress.fragment> }) {
  const [open, setOpen] = useState(false)
  const { lng } = React.useContext(ApplicationPropsContext)
  const { t } = useTranslation(lng, 'Pages')

  const { logo, stamp, board, capTable, committees, incentives } = readFragment(OnboardingProgress.fragment, data)

  const onboarding = [logo, stamp, board, capTable, committees, incentives]
  const totalSteps = onboarding.length
  const completedSteps = onboarding.filter(Boolean).length
  const percentComplete = Math.round((completedSteps / totalSteps) * 100)

  return (
    <>
      <Flex
        bg='#00263A'
        color='white'
        px='1.5rem'
        py={{ base: '1.2rem', md: '0.75rem' }}
        borderRadius={{ base: '14px 14px 0 0', md: 'full' }}
        align='center'
        gap='1rem'
        fontWeight='bold'
        fontSize='md'
        w='100%'
        justifyContent='center'
        cursor='pointer'
        onClick={() => setOpen(true)}
        position={{ base: 'fixed', md: 'static' }}
        bottom={{ base: '0', md: 'auto' }}
        left={{ base: '50%', md: 'auto' }}
        transform={{ base: 'translateX(-50%)', md: 'none' }}
        zIndex='sticky'>
        <Flex w={{ base: 'auto', md: '100%' }} justifyContent='space-between' alignItems='center'>
          <Flex justifyContent='center'>
            <Text>{t('onboarding.onboarding')}</Text>
          </Flex>
          <Flex justifyContent='center'>
            <Separator orientation='vertical' borderColor='white' h='1.5rem' />
          </Flex>
          <Flex justifyContent='center'>
            <Text>
              {percentComplete}% {t('onboarding.complete')}
            </Text>
          </Flex>
        </Flex>
        <Box display={{ base: 'block', md: 'none' }}>
          <FaChevronUp />
        </Box>
      </Flex>

      <OnboardingModal
        setOpen={setOpen}
        open={open}
        percentComplete={percentComplete}
        onboarding={{
          logo,
          stamp,
          board,
          capTable,
          committees,
          incentives,
        }}
      />
    </>
  )
}
