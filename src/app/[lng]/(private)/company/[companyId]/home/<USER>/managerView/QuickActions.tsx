import {
  CalendarEditIcon,
  DocumentArrowIcon,
  FilterTableIcon,
  QuickActionsIcon,
  VideoCardIcon,
} from '@/components/EbanaIcons'
import Modal from '@/components/modal'
import { Tooltip } from '@/components/ui/tooltip'
import { CompanyContext } from '@/context/company'
import { FragmentOf, graphql, readFragment } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, CloseButton, Flex, Link, Stack, Text, useMediaQuery } from '@chakra-ui/react'
import { route } from 'nextjs-routes'
import React from 'react'

QuickActionsSection.fragment = graphql(`
  fragment QuickActionsSection on CompanyManagement {
    register {
      xlsx {
        url
      }
    }
    equityIncentiveReports {
      financialReportXlsx {
        url
      }
    }
    onboarding {
      logo
      stamp
      board
      capTable
      committees
      incentives
    }
  }
`)

export function QuickActionsSection({ data }: { data: FragmentOf<typeof QuickActionsSection.fragment> }) {
  const { t, lng } = useEbanaLocale()
  const { id } = React.useContext(CompanyContext)
  const [open, setOpen] = React.useState(false)

  const { register, equityIncentiveReports, onboarding } = readFragment(QuickActionsSection.fragment, data)

  const [isSmallScreen] = useMediaQuery(['(max-width: 48em)'])

  const quickActions = [
    {
      show: onboarding.capTable,
      label: t('quick_actions.assembly_meeting'),
      tooltip: t('quick_actions.assembly_meeting_tooltip'),
      icon: <CalendarEditIcon boxSize='20px' />,
      link: route({
        pathname: '/[lng]/company/[companyId]/assemblies',
        query: { lng, companyId: id },
      }),
    },
    {
      show: onboarding.board,
      label: t('quick_actions.board_meeting'),
      tooltip: t('quick_actions.board_meeting_tooltip'),
      icon: <VideoCardIcon boxSize='20px' />,
      link: route({
        pathname: '/[lng]/company/[companyId]/board/meetings/new',
        query: { lng, companyId: id },
      }),
    },
    {
      show: onboarding.capTable,
      label: t('quick_actions.export_captable'),
      tooltip: t('quick_actions.export_captable_tooltip'),
      icon: <DocumentArrowIcon boxSize='20px' />,
      link: register?.xlsx?.url,
    },
    {
      show: onboarding.incentives,
      label: t('quick_actions.export_esop'),
      tooltip: t('quick_actions.export_esop_tooltip'),
      icon: <DocumentArrowIcon boxSize='20px' />,
      link: equityIncentiveReports?.financialReportXlsx?.url,
    },
    {
      show: onboarding.logo,
      label: t('quick_actions.add_news'),
      tooltip: t('quick_actions.add_news_tooltip'),
      icon: <FilterTableIcon boxSize='20px' />,
      link: route({
        pathname: '/[lng]/company/[companyId]/profile/news/create',
        query: { lng, companyId: id },
      }),
    },
  ]

  if (!onboarding.incentives || !onboarding.logo || !onboarding.capTable || !onboarding.board) return null

  const largeScreenComponent = (
    <Box w='100%'>
      <Text fontWeight='600' textStyle='body1' mb='1rem' color='text.typo1'>
        {t('quick_actions.title')}
      </Text>
      <Flex flexDir={{ base: 'column', md: 'row' }} gap='11.5px' minW='fit-content'>
        {quickActions
          .filter((action) => action.show)
          .map((action, idx) => {
            return (
              <Link w={{ base: '100%', md: '20%' }} key={idx} textDecoration='none' href={action.link}>
                <Tooltip
                  content={action.tooltip}
                  positioning={{ placement: 'top' }}
                  contentProps={{
                    fontWeight: 600,
                    bg: 'white',
                    color: 'black',
                    borderRadius: 'md',
                    boxShadow: 'md',
                    px: '.8rem',
                    py: '0.6rem',
                  }}>
                  <Stack
                    gap={0}
                    bg='white'
                    borderRadius='1rem'
                    p='12px'
                    w='100%'
                    h='70px'
                    boxShadow='sm'
                    _hover={{ bg: '#D2D4F8', boxShadow: 'sm' }}>
                    {action.icon}
                    <Text lineClamp={1} mt='0.2rem' fontWeight='600' textStyle='h6' color='text.typo1'>
                      {action.label}
                    </Text>
                  </Stack>
                </Tooltip>
              </Link>
            )
          })}
      </Flex>
    </Box>
  )

  const smallScreenComponent = (
    <Box>
      <Box
        onClick={() => setOpen(true)}
        bg='#5858FF'
        position='fixed'
        right='1rem'
        bottom='5rem'
        zIndex={1000}
        width='3.5rem'
        height='3.5rem'
        rounded='full'
        display='flex'
        justifyContent='center'
        alignItems='center'
        shadow='md'
        cursor='pointer'>
        <QuickActionsIcon width='25px' height='43px' />
      </Box>

      <Modal
        open={open}
        setOpen={setOpen}
        Body={
          <Box p={4}>
            <Flex alignItems='center' justifyContent='space-between' mb={2}>
              <Box fontWeight='600' fontSize='xl' mb='1rem' color='text.typo1'>
                {t('quick_actions.title')}
              </Box>

              <CloseButton onClick={() => setOpen(false)} mt='-1rem' />
            </Flex>

            <Flex flexDirection='column' gap='1rem'>
              {quickActions.map((action, idx) => {
                if (!action.show) return null
                return (
                  <Link w={{ base: '100%', md: '13.125rem' }} key={idx} textDecoration='none' href={action.link}>
                    <Flex
                      alignItems='center'
                      gap={2}
                      bg='white'
                      borderRadius='1rem'
                      p='0.75rem'
                      w='100%'
                      h='3.25rem'
                      border='1px solid #E2E8F0'
                      onClick={(e) => {
                        window.location.href = action.link as string
                        e.preventDefault()
                        e.stopPropagation()
                        setOpen(false)
                      }}
                      _hover={{ bg: '#D2D4F8', boxShadow: 'sm' }}>
                      {action.icon}
                      <Text lineClamp={1} fontWeight='600' fontSize='h6' color='text.typo1'>
                        {action.label}
                      </Text>
                    </Flex>
                  </Link>
                )
              })}
            </Flex>
          </Box>
        }
        size='md'
        style={{
          marginInline: 'unset',
          maxWidth: '30rem',
          position: 'fixed',
          bottom: '0',
          padding: '0',
        }}
      />
    </Box>
  )

  return isSmallScreen ? smallScreenComponent : largeScreenComponent
}
