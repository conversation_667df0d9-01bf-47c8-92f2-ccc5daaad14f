'use client'

import DatePicker from '@/components/datetime/DatePicker'
import { DateTime } from '@/components/datetime/DateTime'
import * as icons from '@/components/EbanaIcons'
import { ClockIcon } from '@/components/EbanaIcons'
import { NewStatus } from '@/components/NewStatus'
import { Tooltip } from '@/components/ui/tooltip'
import { useAuth } from '@/context/new-auth'
import { Box, Button, Code, Flex, Grid, Stack, Tabs } from '@chakra-ui/react'
import React from 'react'
import { AreaChartComponent } from './components/AreaChartComponent'
import { CustomAutoComplete } from './components/AutoComplete'
import Avatars from './components/Avatars'
import DateRootUsage from './components/DateRoot'
import FileFormField from './components/FileFormField'
import { Files } from './components/Files'
import { CustomInputGroup } from './components/InputGroup'
import { CustomPagination } from './components/Pagination'
import { PieChartComponent } from './components/PieChartComponent'
import { Sparkline } from './components/Sparkline'
import { Tabs as DocumentTab } from './components/Tabs'
import { SimpleTextFormFields, TextFormFields } from './components/TextFormFields'
export default function DesignSystemPage() {
  const [component, setComponent] = React.useState<{
    id: number
    content: React.ReactNode
    snippet?: string
  }>({
    id: 1,
    content: <DatePicker onChangeToIso={(v) => console.log(v)} />,
    snippet: `import DatePicker from '@/components/datetime/DatePicker' 
          
          <DatePicker onChangeToIso={(v) => console.log(v)} />`,
  })

  // It can be useful
  const auth = useAuth()

  return (
    <Flex height='100vh'>
      <Stack border='1px solid black' pt='100px' px='50px' alignItems='flex-start'>
        <Button
          variant='plain'
          color={component.id === 1 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 1,
              content: <DatePicker onChangeToIso={(v) => console.log(v)} />,
              snippet: `import DatePicker from '@/components/datetime/DatePicker'

          <DatePicker onChangeToIso={(v) => console.log(v)} />`,
            })
          }>
          Date picker
        </Button>
        <Button
          variant='plain'
          color={component.id === 2 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 2,
              content: <DateTime onChangeToIso={(v) => console.log(v)} />,
              snippet: `import { DateTime } from '@/components/datetime/DateTime'
              
          <DateTime onChangeToIso={(v) => console.log(v)} />`,
            })
          }>
          Date time
        </Button>
        <Button
          variant='plain'
          color={component.id === 3 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 3,
              content: <NewStatus label='Pending' icon={<ClockIcon stroke='#FF8F27' />} bg='#FFE2C0' />,
              snippet: `import { Status } from '@/components/Status'

              <Status label='Pending' icon={<ClockIcon stroke='#FF8F27' />} bg='#FFE2C0' />`,
            })
          }>
          Status
        </Button>
        <Button
          variant='plain'
          color={component.id === 4 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 4,
              content: <DocumentTab />,
              snippet: `import { Tabs } from '@chakra-ui/react'
              import React from 'react'

              const [value, setValue] = React.useState('1')

              return <Tabs.Root value={value} onValueChange={({value}) => setValue(value)}>
                <Tabs.List>
                  <Tabs.Trigger value='1'>One</Tabs.Trigger>
                  <Tabs.Trigger value='2'>Two</Tabs.Trigger>
                </Tabs.List>
                <Tabs.Content value='1'>One Content</Tabs.Content>
                <Tabs.Content value='2'>Two Content</Tabs.Content>
              </Tabs.Root>`,
            })
          }>
          Tabs
        </Button>
        <Button
          variant='plain'
          color={component.id === 5 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 5,
              content: <Files />,
              snippet: `import { File } from '@/components/file'

              <File name='File Name' url={fetched form backend} size={10} type='application/pdf' />

              <File name='File Name' url={fetched form backend} size={10} type='image/png' />

              <File name='File Name' url='' size={10} type='image/png'>
                    <IconButton variant='plain'>
                      <UpdateIcon w='1.2rem' h='1.2rem' />
                    </IconButton>
                  </File>`,
            })
          }>
          File
        </Button>
        <Button
          variant='plain'
          color={component.id === 6 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 6,
              content: <Avatars />,
              snippet: `import Avatar from '@/components/Avatar'
                        import Persona from '@/components/Persona'
                        
                        <Avatar name='Amer Alghfaili' />
                        <Avatar name='Amer Alghfaili' badge={true} />
                        <Avatar name='Amer Alghfaili' badge='/assets/visa.png' />
                        <Avatar name='Amer Alghfaili' isEbana={true} />
                        <Persona name='Omar Aloraini' addon='<EMAIL>' Avatar={<Avatar name='Omar Aloraini' />} />
                        <AvatarGroup
                          avatars={list.map((name) => {
                            return <Avatar key={name} name={name} />
                          })}
                          max={3}
                        />
                        <AvatarGroup
                          avatars={list.map((name) => {
                            return <Avatar key={name} name={name} />
                          })}
                          max={3}
                          OverflowIndicator={(remaining) => <Avatar name={remaining.toString()} isEbana={true} width='5em' height='5em' />}
                        />`,
            })
          }>
          Avatars
        </Button>
        <Button
          variant='plain'
          color={component.id === 7 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 7,
              content: (
                <Sparkline
                  data={[
                    { date: '2023-01', value: 145.43 },
                    { date: '2023-02', value: 151.73 },
                    { date: '2023-03', value: 157.65 },
                    { date: '2023-04', value: 169.68 },
                    { date: '2023-05', value: 173.75 },
                    { date: '2023-06', value: 186.68 },
                    { date: '2023-07', value: 181.99 },
                    { date: '2023-08', value: 189.46 },
                  ]}
                  series={[{ name: 'value', color: 'green.solid' }]}
                />
              ),
              snippet: `import Sparkline from '@/components/Sparkline'

              <Sparkline 
                data={[
                  { date: "2023-01", value: 145.43 },
                  { date: "2023-02", value: 151.73 },
                  { date: "2023-03", value: 157.65 },
                  { date: "2023-04", value: 169.68 },
                  { date: "2023-05", value: 173.75 },
                  { date: "2023-06", value: 186.68 },
                  { date: "2023-07", value: 181.99 },
                  { date: "2023-08", value: 189.46 },
                ]} 
                series={[{ name: "value", color: "green.solid" }]}
              />`,
            })
          }>
          Sparkline
        </Button>

        <Button
          variant='plain'
          color={component.id === 7 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 7,
              content: (
                <Sparkline
                  data={[
                    { date: '2023-01', value: 145.43 },
                    { date: '2023-02', value: 151.73 },
                    { date: '2023-03', value: 157.65 },
                    { date: '2023-04', value: 169.68 },
                    { date: '2023-05', value: 173.75 },
                    { date: '2023-06', value: 186.68 },
                    { date: '2023-07', value: 181.99 },
                    { date: '2023-08', value: 189.46 },
                  ]}
                  series={[{ name: 'value', color: 'green.solid' }]}
                />
              ),
              snippet: `import Sparkline from '@/components/Sparkline'

              <Sparkline 
                data={[
                  { date: "2023-01", value: 145.43 },
                  { date: "2023-02", value: 151.73 },
                  { date: "2023-03", value: 157.65 },
                  { date: "2023-04", value: 169.68 },
                  { date: "2023-05", value: 173.75 },
                  { date: "2023-06", value: 186.68 },
                  { date: "2023-07", value: 181.99 },
                  { date: "2023-08", value: 189.46 },
                ]} 
                series={[{ name: "value", color: "green.solid" }]}
              />`,
            })
          }>
          Sparkline
        </Button>
        <Button
          variant='plain'
          color={component.id === 8 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 8,
              content: (
                <AreaChartComponent
                  data={[
                    { windows: 186, mac: 80, linux: 120, month: 'January' },
                    { windows: 165, mac: 95, linux: 110, month: 'February' },
                    { windows: 190, mac: 87, linux: 125, month: 'March' },
                    { windows: 195, mac: 88, linux: 130, month: 'May' },
                    { windows: 182, mac: 98, linux: 122, month: 'June' },
                    { windows: 175, mac: 90, linux: 115, month: 'August' },
                    { windows: 180, mac: 86, linux: 124, month: 'October' },
                    { windows: 185, mac: 91, linux: 126, month: 'November' },
                  ]}
                  series={[
                    { name: 'windows', color: 'teal.solid' },
                    { name: 'mac', color: 'purple.solid' },
                    { name: 'linux', color: 'orange.solid' },
                  ]}
                  xAxisKey='month'
                />
              ),
              snippet: `import AreaChartComponent from '@/components/AreaChartComponent'

              <AreaChartComponent 
                data={[
                { windows: 186, mac: 80, linux: 120, month: "January" },
                  { windows: 165, mac: 95, linux: 110, month: "February" },
                  { windows: 190, mac: 87, linux: 125, month: "March" },
                  { windows: 195, mac: 88, linux: 130, month: "May" },
                  { windows: 182, mac: 98, linux: 122, month: "June" },
                  { windows: 175, mac: 90, linux: 115, month: "August" },
                  { windows: 180, mac: 86, linux: 124, month: "October" },
                  { windows: 185, mac: 91, linux: 126, month: "November" },
                ]} 
                series={[
                  { name: "windows", color: "teal.solid" },
                  { name: "mac", color: "purple.solid" },
                  { name: "linux", color: "orange.solid" },
                ]}
                xAxisKey='month'
              />`,
            })
          }>
          AreaChart
        </Button>
        <Button
          variant='plain'
          color={component.id === 9 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 9,
              content: (
                <PieChartComponent
                  data={[
                    { name: 'windows', value: 400, color: 'blue.solid' },
                    { name: 'mac', value: 300, color: 'orange.solid' },
                    { name: 'linux', value: 300, color: 'pink.solid' },
                    { name: 'other', value: 200, color: 'green.solid' },
                  ]}
                />
              ),
              snippet: `import PieChartComponent from '@/components/PieChartComponent'

              <PieChartComponent data={[
                  { name: "windows", value: 400, color: "blue.solid" },
                  { name: "mac", value: 300, color: "orange.solid" },
                  { name: "linux", value: 300, color: "pink.solid" },
                  { name: "other", value: 200, color: "green.solid" },
                ]}
              />`,
            })
          }>
          PieChart
        </Button>
        {/* /// pagination  */}
        <Button
          variant='plain'
          color={component.id === 10 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 10,
              content: <CustomPagination />,
              snippet: `import { ButtonGroup, IconButton, Pagination } from "@chakra-ui/react"
                        import { LuChevronLeft, LuChevronRight } from "react-icons/lu"

                        export function CustomPagination() {

                          return (
                            <Pagination.Root count={20} pageSize={2} defaultPage={1}>
                              <ButtonGroup variant="ghost" size="sm">
                                <Pagination.PrevTrigger asChild>
                                  <IconButton>
                                    <LuChevronLeft />
                                  </IconButton>
                                </Pagination.PrevTrigger>

                                <Pagination.Items
                                  render={(page) => (
                                    <IconButton variant={{ base: "ghost", _selected: "outline" }}>
                                      {page.value}
                                    </IconButton>
                                  )}
                                />

                                <Pagination.NextTrigger asChild>
                                  <IconButton>
                                    <LuChevronRight />
                                  </IconButton>
                                </Pagination.NextTrigger>
                              </ButtonGroup>
                            </Pagination.Root>
                          )
                            }`,
            })
          }>
          Pagination
        </Button>

        {/* /// input group */}
        <Button
          variant='plain'
          color={component.id === 11 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 11,
              content: <CustomInputGroup />,
              snippet: `import { Input, InputGroup, NativeSelect } from '@chakra-ui/react'
                        import { LuUser } from "react-icons/lu"

                        const DomainSelect = () => (
                          <NativeSelect.Root size="xs" variant="plain" width="auto" me="-1">
                            <NativeSelect.Field defaultValue=".com" fontSize="sm">
                              <option value=".com">.com</option>
                              <option value=".org">.org</option>
                              <option value=".net">.net</option>
                            </NativeSelect.Field>
                            <NativeSelect.Indicator />
                          </NativeSelect.Root>
                        )

                        export function CustomInputGroup() {
                          return (
                            <>
                              <InputGroup endAddon='.com'>
                                <Input placeholder='yoursite' />
                              </InputGroup>
                        
                              <InputGroup  mt={2}  startElement={<LuUser />}>
                              <Input placeholder="Username" />
                            </InputGroup>

                            <InputGroup flex="1" mt={2} startElement="https://" endElement={<DomainSelect />}>
                              <Input ps="4.75em" pe="0"  />
                            </InputGroup>
                            </>
                          )
                        }
                        `,
            })
          }>
          Input Group
        </Button>

        {/* /// Date Root */}
        <Button
          variant='plain'
          color={component.id === 12 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 12,
              content: <DateRootUsage />,
              snippet: `import DateRoot from '@/components/datetime/DateRoot'
                        import ClockFormatRoot from '@/components/datetime/RadioRoot'
                        import TimeRoot from '@/components/datetime/TimeRoot'
                        import { Stack } from '@chakra-ui/react'

                        function DateRootUsage() {
                          return (
                            <Stack position='relative' width='60%' py={4} mx='auto' alignContent='center' display='flex'>
                              <DateRoot
                                dateLabel='Date'
                                dateProps={{ day: 1, month: 1, year: 2025 }}
                                hourValue={12}
                                minuteValue={30}
                                clockFormat='pm'
                                onChangeIso={(i) => {
                                  console.log(i, 'ffffff')
                                }}
                                display='flex'
                                gap='1em'
                                alignItems='center'
                                w='80%'>
                                <TimeRoot display='flex' gap='1em' />
                                <ClockFormatRoot display='flex' gap='1em' flexDirection='row' />
                              </DateRoot>
                            </Stack>
                          )
                        }

                        export default DateRootUsage

                        `,
            })
          }>
          Date Root
        </Button>

        <Button
          variant='plain'
          color={component.id === 13 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 13,
              content: <CustomAutoComplete />,
              snippet: `
                      import {
                        Badge,
                        Combobox,
                        Portal,
                        Wrap,
                        createListCollection,
                      } from "@chakra-ui/react"
                      import { useMemo, useState } from "react"

                      const skills = [
                        "JavaScript",
                        "TypeScript",
                        "React",
                        "Node.js",
                        "GraphQL",
                        "PostgreSQL",
                      ]

                      export const CustomAutoComplete = () => {
                        const [searchValue, setSearchValue] = useState("")
                        const [selectedSkills, setSelectedSkills] = useState<string[]>([])

                        const filteredItems = useMemo(
                          () =>
                            skills.filter((item) =>
                              item.toLowerCase().includes(searchValue.toLowerCase()),
                            ),
                          [searchValue],
                        )

                        const collection = useMemo(
                          () => createListCollection({ items: filteredItems }),
                          [filteredItems],
                        )

                        const handleValueChange = (details: Combobox.ValueChangeDetails) => {
                          setSelectedSkills(details.value)
                        }

                        return (
                          <Combobox.Root
                            multiple
                            closeOnSelect
                            width="320px"
                            value={selectedSkills}
                            collection={collection}
                            onValueChange={handleValueChange}
                            onInputValueChange={(details) => setSearchValue(details.inputValue)}
                          >
                            <Wrap gap="2">
                              {selectedSkills.map((skill) => (
                                <Badge key={skill}>{skill}</Badge>
                              ))}
                            </Wrap>

                            <Combobox.Label>Select Skills</Combobox.Label>

                            <Combobox.Control>
                              <Combobox.Input />
                              <Combobox.IndicatorGroup>
                                <Combobox.Trigger />
                              </Combobox.IndicatorGroup>
                            </Combobox.Control>

                            <Portal>
                              <Combobox.Positioner>
                                <Combobox.Content>
                                  <Combobox.ItemGroup>
                                    {/* <Combobox.ItemGroupLabel>Skills</Combobox.ItemGroupLabel> */}
                                    {filteredItems.map((item) => (
                                      <Combobox.Item key={item} item={item}>
                                        {item}
                                        <Combobox.ItemIndicator />
                                      </Combobox.Item>
                                    ))}
                                    <Combobox.Empty>No skills found</Combobox.Empty>
                                  </Combobox.ItemGroup>
                                </Combobox.Content>
                              </Combobox.Positioner>
                            </Portal>
                          </Combobox.Root>
                        )
                      }`,
            })
          }>
          AutoComplete
        </Button>
        <Button
          variant='plain'
          color={component.id === 14 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 14,
              content: (
                <Grid
                  p='1rem'
                  mb='1rem'
                  display='grid'
                  gridTemplateColumns='repeat(auto-fill, minmax(60px, 1fr))'
                  gap={10}
                  justifyItems='center'
                  alignItems='center'>
                  {Object.values(icons).map((IconComponent, i) => {
                    const iconType = IconComponent.displayName || IconComponent.name || 'Icon'
                    return (
                      <Tooltip key={iconType + '-' + i} contentProps={{ textStyle: 'body3' }} content={iconType}>
                        <Box
                          key={iconType + '-' + i}
                          border='#00000030'
                          borderRadius='8px'
                          p={2}
                          display='flex'
                          alignItems='center'
                          justifyContent='center'
                          bg='#00000030'>
                          <IconComponent />
                        </Box>
                      </Tooltip>
                    )
                  })}
                </Grid>
              ),
              snippet: `
              import { EbanaIcon } from '@/components/EbanaIcons'
              
              export default function Icon() {
                return <EbanaIcon />
              }
                `,
            })
          }>
          Icons
        </Button>
        <Button
          variant='plain'
          color={component.id === 15 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 15,
              content: <TextFormFields />,
              snippet: `
              const field: FormFieldType = {
                label: 'Name',
                status: 'REQUIRED',
                statusMessage: "It's required!!!",
                path: 'name',
                tooltip: 'Your full name',
              }

              const fieldArr: FormFieldType = {
                label: 'Name',
                status: 'REQUIRED',
                statusMessage: "It's required!!!",
                path: 'names',
                tooltip: 'Your full name',
              }

              const fieldItem: FormFieldType = {
                label: 'Name',
                status: 'REQUIRED',
                statusMessage: "It's required!!!",
                path: 'names.$.value',
                tooltip: 'Your full name',
              }

              const form = useForm()

              const { fields, append, remove } = useFieldArray({ name: fieldArr.path })
              
              <EbanaForm form={form} ...>
                <Form.Field field={field}>
                  <Flex>
                    <Form.Label label={field.label} />
                    <Tooltip content={field.tooltip}>
                      <Box>
                        <TooltipIcon />
                      </Box>
                    </Tooltip>
                  </Flex>
                </Form.Field>

                <Form.Field field={field}>
                  <Flex>
                    <Form.Label
                      label={
                        <Flex>
                          <Box color='typography.100'>{field.label}</Box>
                          <CheckIcon width='0.625rem' height='0.5rem' stroke='white'/>
                        </Flex>
                      }
                    />
                    <Tooltip content={field.tooltip}>
                      <Box>
                        <TooltipIcon />
                      </Box>
                    </Tooltip>
                  </Flex>
                </Form.Field>

                <Stack>
                  <Button size='sm' alignSelf='flex-start' p='.5em 1.5em' onClick={() => append({ value: '' })}>
                    Add
                  </Button>
                  {fields.map((f, index) => {
                    return (
                      <Flex key={f.id} gap='.5em'>
                        <Form.Field field={fieldItem} indices={[index]}>
                          <Flex>
                            <Form.Label>{fieldItem.label}</Form.Label>
                            <Tooltip content={fieldItem.tooltip}>
                              <Box>
                                <TooltipIcon />
                              </Box>
                            </Tooltip>
                          </Flex>
                          <TextInput />
                        </Form.Field>
                        <IconButton variant='plain' onClick={() => remove(index)}>
                          <DeleteIcon />
                        </IconButton>
                      </Flex>
                    )
                  })}
                </Stack>
              </EbanaForm>
              `,
            })
          }>
          Text Form Field
        </Button>
        <Button
          variant='plain'
          color={component.id === 16 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 16,
              content: <SimpleTextFormFields />,
              snippet: `
                const form = useForm()
                
                <FormProvider {...form}>
                  <Stack gap='3em'>
                    <Stack>
                      <Box>Normal</Box>
                      <SimpleField field={field}>
                        <TextInput />
                      </SimpleField>
                    </Stack>
                    <Stack>
                      <Box>Without Label</Box>
                      <SimpleField field={field} withLabel={false}>
                        <TextInput />
                      </SimpleField>
                    </Stack>
                      <Box>With Tooltip</Box>
                      <SimpleField field={field} withTooltip={true} tooltipProps={{ showArrow: false }}>
                        <TextInput />
                      </SimpleField>
                    </Stack>
                  </Stack>
                </FormProvider>
              `,
            })
          }>
          Simple Text Form Field
        </Button>
        <Button
          variant='plain'
          color={component.id === 17 && 'primary.200'}
          onClick={() =>
            setComponent({
              id: 17,
              content: <FileFormField />,
              snippet: `
                const form = useForm()
                
                <FormProvider {...form}>
                  <Field.Root field={field}>
                    <Field.Label>{field.label}</Field.Label>
                    <FileInput />
                  </Field.Root>
                </FormProvider>
              `,
            })
          }>
          File Form Field
        </Button>
      </Stack>
      <Stack mt='50px' mx='100px' flex={4} gap='70px'>
        <Box border='1px solid' borderColor='stroke.100' p='20px' borderRadius='12px'>
          {component.content}
        </Box>
        <Tabs.Root defaultValue='code'>
          <Tabs.List>
            <Tabs.Trigger value='code'>Code</Tabs.Trigger>
            <Tabs.Trigger value='props'>Props</Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value='code'>
            <Code px='20px' pb='10px' pt='13px'>
              {component.snippet}
            </Code>
          </Tabs.Content>
          <Tabs.Content value='props'>Soon 👀</Tabs.Content>
        </Tabs.Root>
      </Stack>
    </Flex>
  )
}
