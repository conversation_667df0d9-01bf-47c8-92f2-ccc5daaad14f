import { FileInput } from '@/components/form2/FileInput'
import { Field } from '@/components/form2/FormField'
import { FieldType } from '@/graphql/fragments'
import { FormProvider, useForm } from 'react-hook-form'

export const field: FieldType = {
  __typename: 'FileForm<PERSON>ield',
  path: 'file',
  label: 'file',
  required: true,
  requiredMessage: 'upload a file!',
  available: true,
  status: 'REQUIRED',
  statusMessage: 'upload a file!',
  multiple: true,
  tooltip: 'upload a file!',
}
export default function FileFormField() {
  const form = useForm()

  return (
    <FormProvider {...form}>
      <Field.Root field={field}>
        <Field.Label>{field.label}</Field.Label>
        <FileInput />
      </Field.Root>
    </FormProvider>
  )
}
