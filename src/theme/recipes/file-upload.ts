import { defineSlotRecipe } from '@chakra-ui/react'
import { fileUploadAnatomy } from '@chakra-ui/react/anatomy'

export const fileUploadSlotRecipe = defineSlotRecipe({
  className: 'chakra-file-upload',
  slots: fileUploadAnatomy.keys(),
  base: {
    root: {
      margin: '0 !important',
    },
    dropzone: {
      borderColor: 'primary.200',
      borderStyle: 'dashed',
      background: 'background.300',
      borderRadius: '12px',
      cursor: 'pointer',
      transition: 'all 0.4s ease-in',
      justifyContent: 'flex-start',
      minH: 'none',
      padding: '1rem 1rem',
      _hover: {
        background: 'white',
        borderStyle: 'solid',
      },
      _dragging: {
        background: 'white',
        borderStyle: 'solid',
        outline: 'none',
      },
      _disabled: {
        background: 'background.300',
        borderColor: 'stroke.100',
        borderStyle: 'solid',
        borderWidth: '1px',
        cursor: 'not-allowed',
      },
      focusRing: 'none',
    },
    item: {
      alignItems: 'center',
      justifyContent: 'space-between',
      border: '1px solid #F2F2F4',
      borderRadius: '12px',
      ps: '1rem',
      pe: '1.125em',
      pt: '1rem',
      pb: '0.875em',
    },
    itemName: {
      color: 'typography.100',
      fontWeight: 600,
    },
    itemSizeText: {
      color: 'typography.300',
      textStyle: 'body5',
    },
  },

  defaultVariants: {},
})
