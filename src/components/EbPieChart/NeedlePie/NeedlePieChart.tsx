/* eslint-disable no-shadow */
import { Box } from '@chakra-ui/react'
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from 'recharts'

export default function NeedlePieChart({ degree = 0, size = 1 }: { degree?: number; size?: number }) {
  const data = [{ value: 1 }]
  const cx = 126.5
  const cy = 120
  const iR = 90
  const oR = 110

  return (
    <Box position='relative'>
      {/* chart-needle */}
      <Box className='chart-needle' w='100%' px='7px' position='absolute' bottom={5} transform={`rotate(${degree}deg)`}>
        <svg xmlns='http://www.w3.org/2000/svg' width='9' height='10' viewBox='0 0 9 10' fill='none'>
          <path
            d='M1.63268 9.55294C0.93356 9.87515 0.147809 9.3197 0.218331 8.55314L0.854146 1.64199C0.924667 0.875423 1.79857 0.472666 2.42718 0.917021L8.0945 4.92323C8.7231 5.36759 8.63495 6.32579 7.93583 6.648L1.63268 9.55294Z'
            fill='#8A94A6'
            opacity='0.8'
          />
        </svg>
      </Box>
      <ResponsiveContainer width={264} height={154}>
        <PieChart width={264} height={154}>
          {/* chart background */}
          <Pie
            dataKey='value'
            startAngle={180}
            endAngle={0}
            data={data}
            cx={cx}
            cy={cy}
            innerRadius={iR}
            outerRadius={oR}
            cornerRadius={20}
            fill='#F2F2F4'
            stroke='#F2F2F4'
            strokeWidth={5}
          />

          {/* chart content */}

          <Pie
            dataKey='value'
            startAngle={180}
            endAngle={180 - degree}
            data={data}
            cx={cx}
            cy={cy}
            innerRadius={iR}
            outerRadius={oR}
            cornerRadius={20}
            fill='#F2F2F4'
            stroke='#F2F2F4'
            strokeWidth={5}>
            <Cell key='cell-primary' fill='url(#pie-gradient)' />
          </Pie>
          <defs>
            <linearGradient id='pie-gradient' x1='0' y1='0' x2='1' y2='0'>
              <stop offset='0%' stopColor='#6490EB' />
              <stop offset='100%' stopColor='#5858FF' />
            </linearGradient>
          </defs>
        </PieChart>
      </ResponsiveContainer>
    </Box>
  )
}
