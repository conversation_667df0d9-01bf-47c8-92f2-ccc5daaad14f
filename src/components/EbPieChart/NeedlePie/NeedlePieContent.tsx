import { Box, Stack } from '@chakra-ui/react'

export default function NeedlePieContent({ percentage, description }: { percentage: number; description: string }) {
  return (
    <>
      <Box
        position='absolute'
        top='60%'
        left='50%'
        w='max-content'
        transform='translate(-50%, -50%)'
        height='45%'
        display='flex'
        alignItems='center'
        justifyContent='center'>
        <Stack>
          <Box mx='auto' textStyle='h2' fontWeight={700} color='#00263A'>
            {percentage}%
          </Box>
          <Box
            mx='auto'
            textStyle='body5'
            fontWeight={600}
            bg='#F6F8FF'
            color='#00263A'
            px='8px'
            py='4px'
            borderRadius='8px'>
            {description}
          </Box>
        </Stack>
      </Box>
      <Box position='absolute' left={5} bottom={0} textStyle='body3' fontWeight={600} color='#8A94A6'>
        0%
      </Box>
      <Box position='absolute' right={2.5} bottom={0} textStyle='body3' fontWeight={600} color='#8A94A6'>
        100%
      </Box>
    </>
  )
}
