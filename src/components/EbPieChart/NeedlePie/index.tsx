import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box } from '@chakra-ui/react'
import Needle<PERSON><PERSON><PERSON>hart from './NeedlePieChart'
import NeedlePieContent from './NeedlePieContent'

export default function NeedlePie({
  total = 100,
  value = 0,
  title = 'title',
}: {
  total: number
  value: number
  title: string
}) {
  const { t } = useEbanaLocale()

  const percentage = (value / total) * 100
  const degree = (value / total) * 180

  return (
    <Box dir='ltr' justifyContent='center' position='relative' bg='white' w='264px' h='154px' minW='264px' minH='154px'>
      <NeedlePieChart degree={degree} />
      <NeedlePieContent percentage={Number(percentage.toFixed(0))} description={t(title)} />
    </Box>
  )
}
