import Avatar from '@/components/Avatar'
import { ClockIcon, SimpleArrowIcon } from '@/components/EbanaIcons'
import { graphql } from '@/graphql'
import useDatePicker from '@/hooks/datePicker'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Grid, Stack, Text } from '@chakra-ui/react'
import { FragmentOf, readFragment } from 'gql.tada'

const colors = {
  yellow: '#FEC412',
  blue: '#6490EB',
  red: '#FF5630',
}
Calendar.fragment = graphql(`
  fragment Calendar on Calendar {
    events {
      __typename
      ... on GovernanceMeetingCalendarEvent {
        id
        title
        date {
          relativeShort
          relativeSeconds
          time {
            default
          }
          date {
            default
          }
          month {
            label
          }

          year
          monthOfYear
          dayOfMonth
        }
      }
    }
  }
`)

export default function Calendar({ data }: { data: FragmentOf<typeof Calendar.fragment> }) {
  const { t, lng } = useEbanaLocale()

  const { dayNames, monthDays, selectedYear, selected<PERSON>onth, setSelectedMonth, selectedDay, setSelectedDay, prevDays } =
    useDatePicker(lng)

  const fullDayNames = [
    t('sunday'),
    t('monday'),
    t('tuesday'),
    t('wednesday'),
    t('thursday'),
    t('friday'),
    t('saturday'),
  ]

  const months = [
    { label: t('January'), value: 0 },
    { label: t('February'), value: 1 },
    { label: t('March'), value: 2 },
    { label: t('April'), value: 3 },
    { label: t('May'), value: 4 },
    { label: t('June'), value: 5 },
    { label: t('July'), value: 6 },
    { label: t('August'), value: 7 },
    { label: t('September'), value: 8 },
    { label: t('October'), value: 9 },
    { label: t('November'), value: 10 },
    { label: t('December'), value: 11 },
  ]

  const currentDate = new Date()

  const currentDay = currentDate.getDate()
  const currentDayOfWeek = fullDayNames[currentDate.getDay()]
  const currentMonth = currentDate.getMonth()

  const { events } = readFragment(Calendar.fragment, data)

  const filteredEvents = events.filter(
    (event) =>
      event.date.dayOfMonth === Number(selectedDay) && event.date.monthOfYear === Number(selectedMonth.value) + 1
  )

  const eventDays = monthDays.map(({ dayLabel }: any) => {
    const dayFilteredEvents = events.filter(
      (event) =>
        event.date.dayOfMonth === Number(dayLabel) && event.date.monthOfYear === Number(selectedMonth.value) + 1
    )
    return dayFilteredEvents.map((event) => event.date.relativeSeconds)
  })
  return (
    <Stack bg='white' borderRadius='14px' px='1rem' py='1.25rem'>
      <Stack gap={0} mx='auto'>
        <Box color='primary.200' textStyle='body4' lineHeight={1}>
          {t('today')}
        </Box>
        <Box textStyle='body2' fontWeight={600}>
          {currentDay} {lng === 'en' ? months[currentMonth].label.slice(0, 3) : months[currentMonth].label}
          {', '}
          {currentDayOfWeek}
        </Box>
        <Flex dir='ltr' mt='1rem'>
          <Box
            onClick={() =>
              setSelectedMonth({
                label: months[Number(selectedMonth.value) - 1].label,
                value: Number(selectedMonth.value) - 1,
              })
            }
            maxH='40px'
            maxW='40px'
            backgroundColor={Number(selectedMonth.value) === 0 ? 'transparent' : 'white'}
            borderWidth='1px'
            borderColor={Number(selectedMonth.value) === 0 ? 'transparent' : 'stroke.100'}
            borderStyle='solid'
            textAlign='center'
            height='40px'
            width='40px'
            cursor={false ? null : 'pointer'}
            textStyle='body4'
            fontWeight={500}
            color={Number(selectedMonth.value) === 0 ? 'transparent' : '#00263A'}
            borderRadius='6px'
            alignContent='center'>
            <SimpleArrowIcon
              transform='rotate(180deg)'
              mt='-5px'
              width='18px'
              stroke={Number(selectedMonth.value) === 0 ? 'transparent' : '#00263A'}
            />
          </Box>
          <Box my='auto' mx='auto' fontWeight={600}>
            {selectedMonth.label} {selectedYear.label}
          </Box>
          <Box
            onClick={() =>
              setSelectedMonth({
                label: months[Number(selectedMonth.value) + 1].label,
                value: Number(selectedMonth.value) + 1,
              })
            }
            maxH='40px'
            maxW='40px'
            backgroundColor={Number(selectedMonth.value) === 11 ? 'transparent' : 'white'}
            borderWidth='1px'
            borderColor={Number(selectedMonth.value) === 11 ? 'transparent' : 'stroke.100'}
            borderStyle='solid'
            textAlign='center'
            height='40px'
            width='40px'
            cursor={false ? null : 'pointer'}
            textStyle='body4'
            fontWeight={500}
            color={Number(selectedMonth.value) === 11 ? 'transparent' : '#00263A'}
            borderRadius='6px'
            alignContent='center'>
            <SimpleArrowIcon
              mt='-5px'
              width='18px'
              stroke={Number(selectedMonth.value) === 11 ? 'transparent' : '#00263A'}
            />
          </Box>
        </Flex>
        <Grid
          mt='1rem'
          gridTemplateColumns='repeat(7, 2.5em)'
          gridColumnGap='0.375em'
          gridRowGap='0.3125em'
          mb='1.0625em'>
          {dayNames.map((day) => {
            return (
              <Box key={day} textAlign='center' color='typography.200' textStyle='tagSm'>
                {day}
              </Box>
            )
          })}
        </Grid>
        <Grid gridTemplateColumns='repeat(7, 2.5em)' gridColumnGap='0.375em' gridRowGap='0.3125em'>
          {monthDays.map(({ dayLabel }: any, i) => {
            const isSelected = dayLabel == selectedDay
            const isPrevMonthDay = !dayLabel
            let day
            if (isPrevMonthDay) {
              day = prevDays[i]
            } else {
              day = dayLabel < 10 ? dayLabel.charAt(1) : dayLabel
            }

            return (
              <Stack
                id='day'
                key={i}
                _hover={{ bgColor: isPrevMonthDay ? 'white' : 'rgba(0, 191, 178, 0.10) !important' }}
                backgroundColor={isSelected ? 'rgba(0, 191, 178, 0.10)' : 'white'}
                borderWidth='1px'
                borderStyle='solid'
                borderColor={isSelected ? 'primary.200' : 'stroke.100'}
                textAlign='center'
                height='40px'
                width='40px'
                cursor={isPrevMonthDay ? null : 'pointer'}
                textStyle='body4'
                fontWeight={600}
                color={isPrevMonthDay ? 'typography.300' : '#00263A'}
                borderRadius='6px'
                gap='0'
                onClick={isPrevMonthDay ? null : () => setSelectedDay(dayLabel)}>
                <Box>{day}</Box>
                <Flex gap='2px' mx='auto'>
                  {eventDays[i] &&
                    eventDays[i].map((eventTime, index) => (
                      <Box
                        key={index}
                        borderRadius='50px'
                        bg={
                          Number(eventTime) < 0 ? colors.red : Number(eventTime) < 86400 ? colors.yellow : colors.blue
                        }
                        height='8px'
                        width='8px'
                        // mx='auto'
                        mt='2px'
                      />
                    ))}
                </Flex>
              </Stack>
            )
          })}
        </Grid>
      </Stack>
      {filteredEvents.map((event, index) => {
        const secs = Number(event.date.relativeSeconds)
        const mainColor =
          secs < 0
            ? colors.red // past
            : secs < 86400
              ? colors.yellow // within 24 h
              : colors.blue // later

        const chipBg = `${mainColor}1A`

        return (
          <Flex key={index} mt='1rem' gap='1rem'>
            <Box w='4px' bg={mainColor} borderRadius='2px' />

            <Stack gap='.5rem' py='.4rem' flex='1'>
              {/* time chip */}
              <Flex align='center' bg={chipBg} px='1rem' py='0.5rem' borderRadius='8px' w='fit-content' gap='0.5rem'>
                <ClockIcon stroke={mainColor} />
                <Text color='#00263A'>{event.date.time.default}</Text>
              </Flex>

              {/* title */}
              <Text textStyle='body3' color='typography.100' fontWeight={600}>
                {event.title}
              </Text>

              {/* company row */}
              <Flex align='center' gap='0.75rem'>
                <Avatar boxSize='40px' name={event.title} src='/assets/logo_in_circle.svg' bg='#00263A' />
                <Text textStyle='body3' fontWeight='600' color='typography.100'>
                  {event.title}
                </Text>
              </Flex>
            </Stack>
          </Flex>
        )
      })}
    </Stack>
  )
}
