import { Flex, Separator, Stack } from '@chakra-ui/react'

export default function ResponsiveCard({
  withHeader = true,
  headerContent,
  mainContent,
}: {
  withHeader?: boolean
  headerContent?: React.ReactNode
  mainContent: React.ReactNode
}) {
  return (
    <Stack gap={0} bg='white' borderRadius='14px'>
      {withHeader && (
        <>
          <Flex
            px={{ base: '16px', md: '28px' }}
            pt={{ base: '16px', md: '16px' }}
            pb={{ base: '16px', md: '12px' }}
            display='flex'
            justifyContent='space-between'
            gap={{ base: '1em', md: '0' }}
            fontWeight={600}
            textStyle='body1'>
            {headerContent}
          </Flex>
          <Separator borderColor='gray.200' />
        </>
      )}
      <Stack py={{ base: '16px', md: '20px' }} px={{ base: '16px', md: '28px' }}>
        {mainContent}
      </Stack>
    </Stack>
  )
}
