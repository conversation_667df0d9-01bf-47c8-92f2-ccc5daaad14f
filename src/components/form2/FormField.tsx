import { FieldType } from '@/graphql/fragments'
import { Box, Field as ChakraField, Flex } from '@chakra-ui/react'
import React from 'react'
import { RegisterOptions, useFormContext, UseFormRegisterReturn } from 'react-hook-form'
import { TooltipFilledIcon } from '../EbanaIcons'
import { Tooltip, TooltipProps } from '../ui/tooltip'

const FieldContext = React.createContext<{ field: FieldType; indices?: number[] }>(null)

type UseFieldReturn = {
  register: UseFormRegisterReturn
} & FieldType & {
    required: boolean
    disabled: boolean
    readOnly: boolean
  }

export function useField(options = {} as RegisterOptions): UseFieldReturn {
  const { field, indices } = React.useContext(FieldContext)

  const { register } = useFormContext()
  const rhfOptions: RegisterOptions = { ...options }

  const { path, status, statusMessage, __typename } = field

  const required = status === 'REQUIRED'
  rhfOptions.required = {
    value: required,
    message: required ? statusMessage : null,
  }

  rhfOptions.disabled = status === 'DISABLED'

  if (__typename === 'TextFormField') {
    const { pattern, patternMessage } = field

    rhfOptions.pattern = {
      value: new RegExp(pattern),
      message: patternMessage,
    }
  }

  let resolvedPath = path
  if (indices.length) {
    for (const i of indices) {
      resolvedPath = resolvedPath.replace('$', i.toString())
    }
  }

  return {
    ...field,
    path: resolvedPath,
    register: register(resolvedPath, options),
    required,
    disabled: status === 'DISABLED',
    readOnly: status === 'UNAVAILABLE',
  }
}

type FieldProps = {
  field: FieldType
  indices?: number[]
} & ChakraField.RootProps

function Root({ field, indices = [], ...rest }: FieldProps) {
  const { status } = field

  const { formState } = useFormContext()

  return (
    <FieldContext.Provider value={{ field, indices }}>
      <ChakraField.Root
        invalid={formState.errors != null && Object.keys(formState.errors).length > 0}
        required={status === 'REQUIRED'}
        disabled={status === 'DISABLED'}
        readOnly={status === 'UNAVAILABLE'}
        {...rest}
      />
    </FieldContext.Provider>
  )
}

function Label({ withIndicator = true, children, ...rest }: { withIndicator?: boolean } & ChakraField.LabelProps) {
  const { status } = useField()

  const showRequiredIndicator = withIndicator ? status === 'REQUIRED' : false

  return (
    <ChakraField.Label {...rest}>
      {children}
      {showRequiredIndicator && <ChakraField.RequiredIndicator />}
    </ChakraField.Label>
  )
}

// function HelperText(props: ChakraField.HelperTextProps) {
//   const { helperText } = useField()

//   return <ChakraField.HelperText {...props}>{helperText}</ChakraField.HelperText>
// }

export const Field = {
  Root,
  Label,
  // HelperText,
}

type SimpleFieldProps = {
  withLabel?: boolean
  labelProps?: ChakraField.LabelProps
  withTooltip?: boolean
  tooltipProps?: Partial<TooltipProps>
  withHelperText?: boolean
  helperTextProps?: ChakraField.HelperTextProps
} & FieldProps
export function SimpleField({
  field,
  indices = [],
  withLabel = true,
  labelProps,
  withTooltip = false,
  tooltipProps,
  withHelperText = false,
  helperTextProps,
  children,
  ...rest
}: SimpleFieldProps) {
  return (
    <Field.Root field={field} {...rest}>
      <Flex>
        {withLabel && <Field.Label {...labelProps}>{field.label}</Field.Label>}
        {withTooltip && (
          <Tooltip content={field.tooltip} {...tooltipProps}>
            <Box>
              <TooltipFilledIcon />
            </Box>
          </Tooltip>
        )}
      </Flex>
      {children}
      {/* {withHelperText && <Field.HelperText {...helperTextProps} />} */}
    </Field.Root>
  )
}
