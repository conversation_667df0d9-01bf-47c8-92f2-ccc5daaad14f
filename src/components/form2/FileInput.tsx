import { useFileUpload } from '@chakra-ui/react'
import { useController } from 'react-hook-form'
import { FileUploadDropZone, FileUploadItem, FileUploadItemContent, FileUploadRootProvider } from '../ui/file-upload'
import { useField } from './FormField'

export function FileInput() {
  const field = useField()
  if (field.__typename !== 'FileFormField') throw new Error('unexpected field type')

  const { path, required, disabled, readOnly, multiple } = field

  const {
    field: { onChange },
  } = useController({ name: path })

  const fileUpload = useFileUpload({
    required,
    disabled: disabled || readOnly,
    maxFiles: multiple ? 100 : 1,
    onFileChange: ({ acceptedFiles }) => {
      if (multiple) {
        onChange(acceptedFiles.map((file) => ({ file })))
      } else {
        onChange({ file: acceptedFiles[0] })
      }
    },
  })
  const { acceptedFiles } = fileUpload

  return (
    <FileUploadRootProvider value={fileUpload}>
      <FileUploadDropZone />
      {acceptedFiles.map((file) => {
        return (
          <FileUploadItem key={file.name} file={file} withDelete={true}>
            <FileUploadItemContent />
          </FileUploadItem>
        )
      })}
    </FileUploadRootProvider>
  )
}
