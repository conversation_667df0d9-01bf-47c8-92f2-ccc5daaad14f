import { AppStoreCircleIcon, AppStoreIcon, PlayStoreCircleIcon, PlayStoreIcon } from '@/components/EbanaIcons'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Image, Stack } from '@chakra-ui/react'
import React from 'react'
import { DialogBody, DialogCloseTrigger, DialogContent, DialogHeader, DialogRoot } from '../../ui/dialog'

export default function MobileAd({ isShort = false }: { isShort?: boolean }) {
  const { t, lng } = useEbanaLocale()
  const [open, setOpen] = React.useState<boolean>(false)

  return (
    <>
      {isShort ? (
        <Box
          as='button'
          onClick={() => setOpen(true)}
          dir={lng === 'en' ? 'rtl' : 'ltr'}
          position='relative'
          bg='#1A2234'
          w='224px'
          h='fit-content'
          borderRadius='14px'
          overflow='hidden'>
          <Image position='absolute' width='100%' h='100%' src='/assets/add-app-layer1.png' alt='Background' />
          <Stack
            gap='0.75rem'
            dir={lng === 'en' ? 'ltr' : 'rtl'}
            p='1rem'
            position='relative'
            width='100%'
            height='100%'
            color='white'>
            <Box textAlign='start' lineHeight='1.5rem' fontWeight='semibold' textStyle='body3'>
              {t('try_app')}
            </Box>
            <Flex h='4.5rem' justifyContent='space-between' dir='ltr'>
              <Image src='/assets/add-app-layer2.png' height='6.5rem' mt='-1rem' w='4rem' alt='Background' />
              <Stack w='min-content'>
                <Flex gap='16px' mt='0.33rem'>
                  <AppStoreCircleIcon />
                  <PlayStoreCircleIcon />
                </Flex>
                <Box
                  mx='auto'
                  width='fit-content'
                  mt='auto'
                  lineHeight='1rem'
                  textStyle='body5'
                  fontWeight={700}
                  color='white'>
                  {t('download_now')}
                </Box>
              </Stack>
            </Flex>
          </Stack>
        </Box>
      ) : (
        <Box
          as='button'
          onClick={() => setOpen(true)}
          dir={lng === 'en' ? 'rtl' : 'ltr'}
          position='relative'
          bg='#1A2234'
          w='224px'
          h='176px'
          borderRadius='14px'
          overflow='hidden'>
          <Image position='absolute' width='100%' src='/assets/add-app-layer1.png' alt='Background' />
          <Box
            height='85%'
            ms='auto'
            transform={lng === 'en' ? 'translateX(0px)' : 'translateX(-10px)'}
            mt='15%'
            position='absolute'>
            <Image src='/assets/add-app-layer2.png' alt='Background' />
          </Box>
          <Stack
            gap='0.75rem'
            dir={lng === 'en' ? 'ltr' : 'rtl'}
            p='1.25rem'
            position='relative'
            width='100%'
            height='100%'
            color='white'>
            <Box textAlign='start' lineHeight='1.5rem' fontWeight='semibold' textStyle='body2'>
              {t('try_app')}
            </Box>
            <Flex gap='6px'>
              <AppStoreCircleIcon />
              <PlayStoreCircleIcon />
            </Flex>
            <Box width='fit-content' mt='auto' lineHeight='1rem' textStyle='body5' fontWeight={700} color='white'>
              {t('download_now')}
            </Box>
          </Stack>
        </Box>
      )}

      <DialogRoot open={open} onOpenChange={({ open }) => setOpen(open)} placement='center' size='lg'>
        <DialogContent minW='708px'>
          <DialogHeader p={0} m={0}>
            <Box position='relative' maxH='447px' overflow='hidden' width='100%'>
              <Box zIndex={1}>
                <Image alt='' minW='708px' src='/assets/AdModalBg.png' />
                <Image mx='auto' mt='-50rem' borderWidth='1px' src='/assets/add-app-layer3.png' alt='' />
              </Box>
              <Flex
                p='23px'
                width='447px'
                height='100px'
                mt='-150px'
                borderRadius='20px'
                bg='#6490EBCC'
                ms='130.5px'
                zIndex={2}
                alignContent='center'
                justifyContent='space-evenly'
                position='absolute'>
                <Flex gap='14px' my='auto'>
                  <AppStoreIcon />
                  <Box my='auto' textStyle='h3' color='white'>
                    {t('app_store')}
                  </Box>
                </Flex>
                <Flex gap='14px' my='auto'>
                  <PlayStoreIcon />
                  <Box my='auto' textStyle='h3' color='white'>
                    {t('google_play')}
                  </Box>
                </Flex>
              </Flex>
            </Box>
          </DialogHeader>
          <DialogCloseTrigger color='#8A94A6' top='1.5em' insetEnd='1.5em' />
          <DialogBody padding='0' width='595px' mx='auto'>
            <Box pt='1.5rem' textStyle='h1' fontWeight={700} color='#00263A' pb='15px' mx='auto'>
              {t('get_app_modal_title')}
            </Box>
            <Stack borderRadius={30} borderWidth='1px' borderColor='stroke.100' p='25px'>
              <Box color='#00263A' fontSize='22px' fontWeight={700}>
                {t('get_app')}
              </Box>

              <Box textStyle='h4' fontWeight={700}>
                {t('scan_qr_and_link')}
              </Box>
              <Flex mt='1rem' justifyContent='space-evenly'>
                <a rel='noopener' target='_blank' href='https://apps.apple.com/sa/app/ebana/id6477212852'>
                  <Stack>
                    <Image width='130px' src='/assets/ebana-app-store-link-qr.png' alt='' />

                    <Box textStyle='h4' w='fit-content' mx='auto' fontWeight={700}>
                      {t('on')} {t('app_store')}
                    </Box>
                  </Stack>
                </a>
                <a
                  rel='noopener'
                  target='_blank'
                  href='https://play.google.com/store/apps/details?id=com.ebana.app&hl=en&pli=1'>
                  <Stack>
                    <Image width='130px' src='/assets/ebana-google-play-link-qr.png' alt='' />

                    <Box textStyle='h4' w='fit-content' mx='auto' fontWeight={700}>
                      {t('on')} {t('google_play')}
                    </Box>
                  </Stack>
                </a>
              </Flex>
            </Stack>
          </DialogBody>
        </DialogContent>
      </DialogRoot>
    </>
  )
}
