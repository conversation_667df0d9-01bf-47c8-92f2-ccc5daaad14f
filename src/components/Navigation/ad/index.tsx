import { useAuth } from '@/context/new-auth'
import { Stack } from '@chakra-ui/react'
import MobileAd from './MobileAd'
import { RegisterNewCompany } from './NewRegisterCompany'
import OldRegisterCompany from './OldRegisterCompany'

export default function Ad() {
  const { hasFlag, activeWorkspace } = useAuth()

  const isShort = window.innerHeight < 1005 || window.visualViewport?.height < 1005

  if (hasFlag('dynamic-dashboard'))
    return (
      <>
        <Stack gap='16px' w='fit-content' mx='auto' mb='1.4375em'>
          {activeWorkspace.type === 'individual' && <MobileAd isShort={isShort} />}
          <RegisterNewCompany isShort={isShort} />
        </Stack>
      </>
    )
  else return <OldRegisterCompany />
}
