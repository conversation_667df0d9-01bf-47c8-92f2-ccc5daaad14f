'use client'

import { Box, Flex, FlexProps, useMediaQuery } from '@chakra-ui/react'
import { RouteLiteral } from 'nextjs-routes'
import React from 'react'
import Header from '../header'
import DesktopNavigation from './DesktopNavigation'
import MobileNavigation from './MobileNavigation'

type NavigationContainerProps = {
  bg?: string
  withBack?: boolean
  backLink?: RouteLiteral
  pageVariant?: 'normal' | 'float'
  header?: React.ReactElement
  children: React.ReactNode
} & FlexProps
export default function NavigationContainer(props: NavigationContainerProps) {
  const { children, bg, withBack, backLink, pageVariant = 'float', header, ...rest } = props

  const [isMd] = useMediaQuery(['(min-width: 48em)'])
  const [toggled, setToggled] = React.useState(false)

  let renderedNavigation

  if (isMd) {
    renderedNavigation = (
      <Box height='100%'>
        <DesktopNavigation />
      </Box>
    )
  } else {
    renderedNavigation = (
      <Box height='100%'>
        <MobileNavigation toggled={toggled} setToggled={setToggled} />
      </Box>
    )
  }

  function supplyHeaderProps() {
    const newProps = {
      setToggled,
      withBack,
      backLink,
    }

    return React.cloneElement(header, newProps)
  }

  return (
    <PageDesignContext.Provider
      value={{
        variant: pageVariant,
      }}>
      <Flex position='relative' height='100vh' style={{ backgroundColor: '#EAECF0' }} {...rest}>
        <div id='sidebar'>{renderedNavigation}</div>
        <Box width='100%' overflowY='auto' overflowX='hidden' bg={bg || '#EAECF0'}>
          {header != null ? (
            supplyHeaderProps()
          ) : (
            <Header setToggled={setToggled} withBack={withBack} backLink={backLink} />
          )}
          <main
            style={{
              minHeight: `calc(100vh -  9.1em)`,
            }}>
            {children}
          </main>
        </Box>
      </Flex>
    </PageDesignContext.Provider>
  )
}

type PageDesignContextType = {
  variant: 'float' | 'normal'
}
export const PageDesignContext = React.createContext<PageDesignContextType>({ variant: 'float' })
