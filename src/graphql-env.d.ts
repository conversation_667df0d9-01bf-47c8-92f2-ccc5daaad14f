/* eslint-disable */
/* prettier-ignore */

export type introspection_types = {
    'AddonOrder': { kind: 'OBJECT'; name: '<PERSON>donOrder'; fields: { 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; }; };
    'AddonOrderPayload': { kind: 'OBJECT'; name: 'AddonOrderPayload'; fields: { 'addonOrder': { name: 'addonOrder'; type: { kind: 'OBJECT'; name: 'AddonOrder'; ofType: null; } }; }; };
    'AddonPurchaseForm': { kind: 'OBJECT'; name: 'AddonPurchaseForm'; fields: { 'addon': { name: 'addon'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'quantity': { name: 'quantity'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AddonPurchaseInput': { kind: 'INPUT_OBJECT'; name: 'AddonPurchaseInput'; isOneOf: false; inputFields: [{ name: 'addon'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'quantity'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'AmPm': { name: 'AmPm'; enumValues: 'AM' | 'PM'; };
    'AmPmOption': { kind: 'OBJECT'; name: 'AmPmOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'AmPm'; ofType: null; } }; }; };
    'As': { kind: 'OBJECT'; name: 'As'; fields: { 'assembliesPaginated': { name: 'assembliesPaginated'; type: { kind: 'OBJECT'; name: 'AssemblyPaginated'; ofType: null; } }; 'assembly': { name: 'assembly'; type: { kind: 'OBJECT'; name: 'Assembly'; ofType: null; } }; 'companies': { name: 'companies'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Company'; ofType: null; }; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'dashboard': { name: 'dashboard'; type: { kind: 'OBJECT'; name: 'IndividualDashboard'; ofType: null; } }; 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTrade'; ofType: null; } }; 'directTradesPaginated': { name: 'directTradesPaginated'; type: { kind: 'OBJECT'; name: 'DirectTradePaginated'; ofType: null; } }; 'equities': { name: 'equities'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'AsEquity'; ofType: null; }; } }; 'equity': { name: 'equity'; type: { kind: 'OBJECT'; name: 'AsEquity'; ofType: null; } }; 'pressRelease': { name: 'pressRelease'; type: { kind: 'OBJECT'; name: 'PressRelease'; ofType: null; } }; 'pressReleasesPaginated': { name: 'pressReleasesPaginated'; type: { kind: 'OBJECT'; name: 'PressReleasePaginated'; ofType: null; } }; 'todos': { name: 'todos'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INTERFACE'; name: 'Todo'; ofType: null; }; } }; }; };
    'AsEquity': { kind: 'OBJECT'; name: 'AsEquity'; fields: { 'agent': { name: 'agent'; type: { kind: 'OBJECT'; name: 'EquityAgent'; ofType: null; } }; 'averageCostPerShares': { name: 'averageCostPerShares'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'certificate': { name: 'certificate'; type: { kind: 'OBJECT'; name: 'EquityCertificate'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'marketValue': { name: 'marketValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'policyAccepted': { name: 'policyAccepted'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'position': { name: 'position'; type: { kind: 'OBJECT'; name: 'EquityPosition'; ofType: null; } }; 'positions': { name: 'positions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityPosition'; ofType: null; }; } }; 'profitAndLoss': { name: 'profitAndLoss'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'profitAndLossPercent': { name: 'profitAndLossPercent'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'recentRecipients': { name: 'recentRecipients'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; }; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'totalCost': { name: 'totalCost'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalOwnership': { name: 'totalOwnership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'totalShares': { name: 'totalShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'tradingPolicy': { name: 'tradingPolicy'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; }; };
    'Assembly': { kind: 'OBJECT'; name: 'Assembly'; fields: { 'attendForm': { name: 'attendForm'; type: { kind: 'OBJECT'; name: 'AssemblyAttendForm'; ofType: null; } }; 'boardElection': { name: 'boardElection'; type: { kind: 'OBJECT'; name: 'AssemblyBoardElection'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'companyName': { name: 'companyName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'companyShareCount': { name: 'companyShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; }; } }; 'hasAttended': { name: 'hasAttended'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'items': { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyItem'; ofType: null; }; }; } }; 'meeting': { name: 'meeting'; type: { kind: 'OBJECT'; name: 'ExternalOnlineMeeting'; ofType: null; } }; 'nomineeElection': { name: 'nomineeElection'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'nominees': { name: 'nominees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyNominee'; ofType: null; }; } }; 'resultSummary': { name: 'resultSummary'; type: { kind: 'OBJECT'; name: 'AssemblyResultSummary'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'shareholderVotingShareCount': { name: 'shareholderVotingShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'AssemblyStatusOption'; ofType: null; } }; 'submitVoteForm': { name: 'submitVoteForm'; type: { kind: 'OBJECT'; name: 'AssemblySubmitVoteForm'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'AssemblyTypeOption'; ofType: null; } }; 'votingShareCount': { name: 'votingShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'AssemblyAmendForm': { kind: 'OBJECT'; name: 'AssemblyAmendForm'; fields: { 'additionalData': { name: 'additionalData'; type: { kind: 'OBJECT'; name: 'DefaultItems'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardSeats': { name: 'boardSeats'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'chairRelation': { name: 'chairRelation'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'items': { name: 'items'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_blockedIds': { name: 'items_blockedIds'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'items_generated': { name: 'items_generated'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'items_options': { name: 'items_options'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_options_text': { name: 'items_options_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'items_text': { name: 'items_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingPassword': { name: 'meetingPassword'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingUrl': { name: 'meetingUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'nomination': { name: 'nomination'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'nomination_blockedIds': { name: 'nomination_blockedIds'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'nomination_nominees': { name: 'nomination_nominees'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'nomination_nominees_name': { name: 'nomination_nominees_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'relationManagerId': { name: 'relationManagerId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secretaryId': { name: 'secretaryId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'substantial': { name: 'substantial'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'voteCounterId': { name: 'voteCounterId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'AssemblyAmendInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyAmendInput'; isOneOf: false; inputFields: [{ name: 'substantial'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'date'; type: { kind: 'SCALAR'; name: 'OffsetDateTime'; ofType: null; }; defaultValue: null }, { name: 'voteCounterId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'secretaryId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'relationManagerId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'chairRelation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'meetingUrl'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'meetingPassword'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'nomination'; type: { kind: 'INPUT_OBJECT'; name: 'NominationAmendInput'; ofType: null; }; defaultValue: null }, { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyAmendItemInput'; ofType: null; }; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }, { name: 'boardSeats'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'AssemblyAmendItemInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyAmendItemInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'blockedIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; }; defaultValue: null }, { name: 'generated'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyItemOptionInput'; ofType: null; }; }; defaultValue: null }]; };
    'AssemblyAmendNomineeInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyAmendNomineeInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'AssemblyAnswer': { name: 'AssemblyAnswer'; enumValues: 'AGREE' | 'DISAGREE' | 'NEUTRAL'; };
    'AssemblyAnswerInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyAnswerInput'; isOneOf: false; inputFields: [{ name: 'standardVote'; type: { kind: 'ENUM'; name: 'AssemblyAnswer'; ofType: null; }; defaultValue: null }, { name: 'customVote'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'AssemblyAnswerOption': { kind: 'OBJECT'; name: 'AssemblyAnswerOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'AssemblyAnswer'; ofType: null; } }; }; };
    'AssemblyAttendForm': { kind: 'OBJECT'; name: 'AssemblyAttendForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyAttendance': { kind: 'OBJECT'; name: 'AssemblyAttendance'; fields: { 'attendeeCount': { name: 'attendeeCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'attendees': { name: 'attendees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyAttendee'; ofType: null; }; } }; 'percentageOfCapital': { name: 'percentageOfCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'principalShareCount': { name: 'principalShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'proxyShareCount': { name: 'proxyShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'AssemblyAttendee': { kind: 'OBJECT'; name: 'AssemblyAttendee'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'ownershipToAttendingCapital': { name: 'ownershipToAttendingCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'ownershipToCapital': { name: 'ownershipToCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'AssemblyBoardElection': { kind: 'OBJECT'; name: 'AssemblyBoardElection'; fields: { 'blockedIds': { name: 'blockedIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'boardSeats': { name: 'boardSeats'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'electionSharesCount': { name: 'electionSharesCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'nominees': { name: 'nominees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'NomineeResultSummary'; ofType: null; }; } }; 'votingSharesToAttendingCapital': { name: 'votingSharesToAttendingCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'votingSharesToTotalCapital': { name: 'votingSharesToTotalCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; }; };
    'AssemblyBoardElectionNominee': { kind: 'OBJECT'; name: 'AssemblyBoardElectionNominee'; fields: { 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'percentageOfElectionVotes': { name: 'percentageOfElectionVotes'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'AssemblyCancelForm': { kind: 'OBJECT'; name: 'AssemblyCancelForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'reason': { name: 'reason'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyCancelInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyCancelInput'; isOneOf: false; inputFields: [{ name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'AssemblyChairRelation': { name: 'AssemblyChairRelation'; enumValues: 'CHAIR' | 'DEPUTY_CHAIR' | 'CEO'; };
    'AssemblyChairRelationOption': { kind: 'OBJECT'; name: 'AssemblyChairRelationOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'AssemblyChairRelation'; ofType: null; } }; }; };
    'AssemblyCloseVotingForm': { kind: 'OBJECT'; name: 'AssemblyCloseVotingForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyCreateForm': { kind: 'OBJECT'; name: 'AssemblyCreateForm'; fields: { 'additionalData': { name: 'additionalData'; type: { kind: 'OBJECT'; name: 'DefaultItems'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'autoOpenVoting': { name: 'autoOpenVoting'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardSeats': { name: 'boardSeats'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'chairRelation': { name: 'chairRelation'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'items': { name: 'items'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_blockedIds': { name: 'items_blockedIds'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'items_generated': { name: 'items_generated'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'items_options': { name: 'items_options'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_options_text': { name: 'items_options_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'items_text': { name: 'items_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingPassword': { name: 'meetingPassword'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingUrl': { name: 'meetingUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'nomination': { name: 'nomination'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'nomination_blockedIds': { name: 'nomination_blockedIds'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'nomination_nominees': { name: 'nomination_nominees'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'nomination_nominees_name': { name: 'nomination_nominees_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'relationManagerId': { name: 'relationManagerId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secretaryId': { name: 'secretaryId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'voteCounterId': { name: 'voteCounterId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'votingPower': { name: 'votingPower'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'votingPower_shareClass': { name: 'votingPower_shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxShareClass'; ofType: null; } }; 'votingPower_shares': { name: 'votingPower_shares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; }; };
    'AssemblyCreateInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyCreateInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'ENUM'; name: 'AssemblyType'; ofType: null; }; defaultValue: null }, { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'date'; type: { kind: 'SCALAR'; name: 'OffsetDateTime'; ofType: null; }; defaultValue: null }, { name: 'voteCounterId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'secretaryId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'relationManagerId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'chairRelation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'meetingUrl'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'meetingPassword'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'nomination'; type: { kind: 'INPUT_OBJECT'; name: 'NominationCreateInput'; ofType: null; }; defaultValue: null }, { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyItemCreateInput'; ofType: null; }; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }, { name: 'boardSeats'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'autoOpenVoting'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'votingPower'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyVotingPower'; ofType: null; }; }; defaultValue: null }]; };
    'AssemblyDeleteFileForm': { kind: 'OBJECT'; name: 'AssemblyDeleteFileForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'key': { name: 'key'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyDeleteFileInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyDeleteFileInput'; isOneOf: false; inputFields: [{ name: 'key'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'AssemblyDeleteForm': { kind: 'OBJECT'; name: 'AssemblyDeleteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyFileRef': { kind: 'OBJECT'; name: 'AssemblyFileRef'; fields: { 'attachmentUrl': { name: 'attachmentUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'contentLength': { name: 'contentLength'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'contentType': { name: 'contentType'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'fileDeleteForm': { name: 'fileDeleteForm'; type: { kind: 'OBJECT'; name: 'AssemblyDeleteFileForm'; ofType: null; } }; 'filename': { name: 'filename'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'form': { name: 'form'; type: { kind: 'OBJECT'; name: 'SimpleForm'; ofType: null; } }; 'inlineUrl': { name: 'inlineUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'key': { name: 'key'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'url': { name: 'url'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; }; };
    'AssemblyFilter': { kind: 'INPUT_OBJECT'; name: 'AssemblyFilter'; isOneOf: false; inputFields: [{ name: 'status'; type: { kind: 'ENUM'; name: 'AssemblyStatus'; ofType: null; }; defaultValue: null }, { name: 'status_in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'AssemblyStatus'; ofType: null; }; }; defaultValue: null }, { name: 'status_nin'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'AssemblyStatus'; ofType: null; }; }; defaultValue: null }, { name: 'type'; type: { kind: 'ENUM'; name: 'AssemblyType'; ofType: null; }; defaultValue: null }, { name: 'type_in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'AssemblyType'; ofType: null; }; }; defaultValue: null }, { name: 'type_nin'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'AssemblyType'; ofType: null; }; }; defaultValue: null }, { name: 'and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyFilter'; ofType: null; }; }; }; defaultValue: null }, { name: 'or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyFilter'; ofType: null; }; }; }; defaultValue: null }, { name: 'not'; type: { kind: 'INPUT_OBJECT'; name: 'AssemblyFilter'; ofType: null; }; defaultValue: null }]; };
    'AssemblyFinishForm': { kind: 'OBJECT'; name: 'AssemblyFinishForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyItem': { kind: 'OBJECT'; name: 'AssemblyItem'; fields: { 'blocked': { name: 'blocked'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'discussion': { name: 'discussion'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'isStandardVote': { name: 'isStandardVote'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'summary': { name: 'summary'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'vote': { name: 'vote'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'AssemblyItemCreateInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyItemCreateInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'blockedIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; }; defaultValue: null }, { name: 'generated'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyItemOptionInput'; ofType: null; }; }; defaultValue: null }]; };
    'AssemblyItemOptionInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyItemOptionInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'AssemblyManagement': { kind: 'OBJECT'; name: 'AssemblyManagement'; fields: { 'amendForm': { name: 'amendForm'; type: { kind: 'OBJECT'; name: 'AssemblyAmendForm'; ofType: null; } }; 'assemblyReport': { name: 'assemblyReport'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'assemblyUpdateForm': { name: 'assemblyUpdateForm'; type: { kind: 'OBJECT'; name: 'AssemblyUpdateForm'; ofType: null; } }; 'attendance': { name: 'attendance'; type: { kind: 'OBJECT'; name: 'AssemblyAttendance'; ofType: null; } }; 'attendanceReport': { name: 'attendanceReport'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'boardElection': { name: 'boardElection'; type: { kind: 'OBJECT'; name: 'AssemblyBoardElection'; ofType: null; } }; 'cancelForm': { name: 'cancelForm'; type: { kind: 'OBJECT'; name: 'AssemblyCancelForm'; ofType: null; } }; 'chair': { name: 'chair'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'closeVotingForm': { name: 'closeVotingForm'; type: { kind: 'OBJECT'; name: 'AssemblyCloseVotingForm'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'CompanyManagement'; ofType: null; } }; 'companyName': { name: 'companyName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'companyShareCount': { name: 'companyShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'AssemblyDeleteForm'; ofType: null; } }; 'electionSummary': { name: 'electionSummary'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyFileRef'; ofType: null; }; } }; 'finishForm': { name: 'finishForm'; type: { kind: 'OBJECT'; name: 'AssemblyFinishForm'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'items': { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyManagementItem'; ofType: null; }; }; } }; 'meeting': { name: 'meeting'; type: { kind: 'OBJECT'; name: 'ExternalOnlineMeeting'; ofType: null; } }; 'nomineeElection': { name: 'nomineeElection'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'nominees': { name: 'nominees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyNominee'; ofType: null; }; } }; 'openVotingForm': { name: 'openVotingForm'; type: { kind: 'OBJECT'; name: 'AssemblyOpenVotingForm'; ofType: null; } }; 'postponeForm': { name: 'postponeForm'; type: { kind: 'OBJECT'; name: 'AssemblyPostponeForm'; ofType: null; } }; 'publishForm': { name: 'publishForm'; type: { kind: 'OBJECT'; name: 'AssemblyPublishForm'; ofType: null; } }; 'reports': { name: 'reports'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; }; } }; 'resultSummary': { name: 'resultSummary'; type: { kind: 'OBJECT'; name: 'AssemblyResultSummary'; ofType: null; } }; 'secretary': { name: 'secretary'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'shareholders': { name: 'shareholders'; type: { kind: 'OBJECT'; name: 'AssemblyShareholderPaginated'; ofType: null; } }; 'shortId': { name: 'shortId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'AssemblyStatusOption'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'AssemblyTypeOption'; ofType: null; } }; 'updateMinutesForm': { name: 'updateMinutesForm'; type: { kind: 'OBJECT'; name: 'AssemblyUpdateMinutesForm'; ofType: null; } }; 'uploadFileForm': { name: 'uploadFileForm'; type: { kind: 'OBJECT'; name: 'AssemblyUploadFileForm'; ofType: null; } }; 'voteCards': { name: 'voteCards'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'voteCounter': { name: 'voteCounter'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'voteDate': { name: 'voteDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'voteEndDate': { name: 'voteEndDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'voteReport': { name: 'voteReport'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'votingShareCount': { name: 'votingShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'AssemblyManagementItem': { kind: 'OBJECT'; name: 'AssemblyManagementItem'; fields: { 'blockedIds': { name: 'blockedIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'discussion': { name: 'discussion'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'isStandardVote': { name: 'isStandardVote'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'summary': { name: 'summary'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; }; };
    'AssemblyManagementPaginated': { kind: 'OBJECT'; name: 'AssemblyManagementPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyManagement'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'AssemblyManagementPayload': { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; fields: { 'assembly': { name: 'assembly'; type: { kind: 'OBJECT'; name: 'AssemblyManagement'; ofType: null; } }; }; };
    'AssemblyManagementSort': { kind: 'INPUT_OBJECT'; name: 'AssemblyManagementSort'; isOneOf: false; inputFields: [{ name: 'date'; type: { kind: 'ENUM'; name: 'SortDirection'; ofType: null; }; defaultValue: null }]; };
    'AssemblyMeetingMinutesTodo': { kind: 'OBJECT'; name: 'AssemblyMeetingMinutesTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'AssemblyNominee': { kind: 'OBJECT'; name: 'AssemblyNominee'; fields: { 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'votes': { name: 'votes'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'AssemblyNomineeCreateInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyNomineeCreateInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'AssemblyOpenVotingForm': { kind: 'OBJECT'; name: 'AssemblyOpenVotingForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyPaginated': { kind: 'OBJECT'; name: 'AssemblyPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Assembly'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'AssemblyPayload': { kind: 'OBJECT'; name: 'AssemblyPayload'; fields: { 'assembly': { name: 'assembly'; type: { kind: 'OBJECT'; name: 'Assembly'; ofType: null; } }; }; };
    'AssemblyPostponeForm': { kind: 'OBJECT'; name: 'AssemblyPostponeForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'quorum': { name: 'quorum'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyPostponeInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyPostponeInput'; isOneOf: false; inputFields: [{ name: 'date'; type: { kind: 'SCALAR'; name: 'OffsetDateTime'; ofType: null; }; defaultValue: null }, { name: 'quorum'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'AssemblyPublishForm': { kind: 'OBJECT'; name: 'AssemblyPublishForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyResultSummary': { kind: 'OBJECT'; name: 'AssemblyResultSummary'; fields: { 'attendeeCount': { name: 'attendeeCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'attendeeCount2': { name: 'attendeeCount2'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'attendingShareCount': { name: 'attendingShareCount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'items': { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyResultSummaryItem'; ofType: null; }; } }; 'percentageOfCapital': { name: 'percentageOfCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'principalAttendeeCount': { name: 'principalAttendeeCount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'principalAttendingShares': { name: 'principalAttendingShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'principalVotes': { name: 'principalVotes'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'proxyAttendeeCount': { name: 'proxyAttendeeCount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'proxyAttendingShares': { name: 'proxyAttendingShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'proxyVotes': { name: 'proxyVotes'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'voteShareCount': { name: 'voteShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'AssemblyResultSummaryItem': { kind: 'OBJECT'; name: 'AssemblyResultSummaryItem'; fields: { 'abstainShareCount': { name: 'abstainShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'abstainSharesToVotingCapital': { name: 'abstainSharesToVotingCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'adopted': { name: 'adopted'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'againstShareCount': { name: 'againstShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'againstSharesToVotingCapital': { name: 'againstSharesToVotingCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'forShareCount': { name: 'forShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'forSharesToVotingCapital': { name: 'forSharesToVotingCapital'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'isStandardVote': { name: 'isStandardVote'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'summary': { name: 'summary'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'votes': { name: 'votes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ItemOption'; ofType: null; }; } }; }; };
    'AssemblyShareholder': { kind: 'OBJECT'; name: 'AssemblyShareholder'; fields: { 'attendanceDate': { name: 'attendanceDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'blocked': { name: 'blocked'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; } }; 'delegate': { name: 'delegate'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'lastVoteSubmissionDate': { name: 'lastVoteSubmissionDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'nominationShares': { name: 'nominationShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'nominations': { name: 'nominations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyShareholderNomination'; ofType: null; }; }; } }; 'shares': { name: 'shares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'votes': { name: 'votes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'AssemblyShareholderNomination': { kind: 'OBJECT'; name: 'AssemblyShareholderNomination'; fields: { 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'votes': { name: 'votes'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'AssemblyShareholderPaginated': { kind: 'OBJECT'; name: 'AssemblyShareholderPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'AssemblyShareholder'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'AssemblySort': { kind: 'INPUT_OBJECT'; name: 'AssemblySort'; isOneOf: false; inputFields: [{ name: 'date'; type: { kind: 'ENUM'; name: 'SortDirection'; ofType: null; }; defaultValue: null }]; };
    'AssemblyStatus': { name: 'AssemblyStatus'; enumValues: 'PENDING' | 'OPEN_VOTING' | 'CLOSED_VOTING' | 'CLOSED' | 'CANCELLED' | 'WAITING_APPROVAL'; };
    'AssemblyStatusOption': { kind: 'OBJECT'; name: 'AssemblyStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'AssemblyStatus'; ofType: null; } }; }; };
    'AssemblyStatuses': { name: 'AssemblyStatuses'; enumValues: 'PENDING' | 'OPEN_VOTING' | 'CLOSED_VOTING' | 'CLOSED' | 'CANCELLED' | 'WAITING_APPROVAL'; };
    'AssemblyStatusesOption': { kind: 'OBJECT'; name: 'AssemblyStatusesOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'AssemblyStatuses'; ofType: null; } }; }; };
    'AssemblySubmitVoteForm': { kind: 'OBJECT'; name: 'AssemblySubmitVoteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'nominations': { name: 'nominations'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'nominations_shares': { name: 'nominations_shares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'resolutions': { name: 'resolutions'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'resolutions_customVote': { name: 'resolutions_customVote'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'resolutions_standardVote': { name: 'resolutions_standardVote'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblySubmitVoteInput': { kind: 'INPUT_OBJECT'; name: 'AssemblySubmitVoteInput'; isOneOf: false; inputFields: [{ name: 'resolutions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyAnswerInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'nominations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblySubmitVoteNominationInput'; ofType: null; }; }; }; defaultValue: null }]; };
    'AssemblySubmitVoteNominationInput': { kind: 'INPUT_OBJECT'; name: 'AssemblySubmitVoteNominationInput'; isOneOf: false; inputFields: [{ name: 'shares'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'AssemblySubmitVoteResolutionInput': { kind: 'INPUT_OBJECT'; name: 'AssemblySubmitVoteResolutionInput'; isOneOf: false; inputFields: [{ name: 'vote'; type: { kind: 'ENUM'; name: 'AssemblyAnswer'; ofType: null; }; defaultValue: null }]; };
    'AssemblyType': { name: 'AssemblyType'; enumValues: 'ORDINARY_GENERAL_ASSEMBLY' | 'EXTRAORDINARY_GENERAL_ASSEMBLY' | 'ESTABLISHMENT_ASSEMBLY' | 'PARTNER_ASSEMBLY' | 'PARTNER_RESOLUTION' | 'CIRCULAR_RESOLUTION'; };
    'AssemblyTypeOption': { kind: 'OBJECT'; name: 'AssemblyTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'AssemblyType'; ofType: null; } }; }; };
    'AssemblyUpdateForm': { kind: 'OBJECT'; name: 'AssemblyUpdateForm'; fields: { 'additionalData': { name: 'additionalData'; type: { kind: 'OBJECT'; name: 'DefaultItems'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'autoOpenVoting': { name: 'autoOpenVoting'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardSeats': { name: 'boardSeats'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'chairRelation': { name: 'chairRelation'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'items': { name: 'items'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_blockedIds': { name: 'items_blockedIds'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'items_generated': { name: 'items_generated'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'items_options': { name: 'items_options'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_options_text': { name: 'items_options_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'items_text': { name: 'items_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingPassword': { name: 'meetingPassword'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingUrl': { name: 'meetingUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'nomination': { name: 'nomination'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'nomination_blockedIds': { name: 'nomination_blockedIds'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'nomination_nominees': { name: 'nomination_nominees'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'nomination_nominees_name': { name: 'nomination_nominees_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'relationManagerId': { name: 'relationManagerId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secretaryId': { name: 'secretaryId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'voteCounterId': { name: 'voteCounterId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'votingPower': { name: 'votingPower'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'votingPower_shareClass': { name: 'votingPower_shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxShareClass'; ofType: null; } }; 'votingPower_shares': { name: 'votingPower_shares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; }; };
    'AssemblyUpdateInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyUpdateInput'; isOneOf: false; inputFields: [{ name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'date'; type: { kind: 'SCALAR'; name: 'OffsetDateTime'; ofType: null; }; defaultValue: null }, { name: 'voteCounterId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'secretaryId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'relationManagerId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'chairRelation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'meetingUrl'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'meetingPassword'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'nomination'; type: { kind: 'INPUT_OBJECT'; name: 'NominationUpdateInput'; ofType: null; }; defaultValue: null }, { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyUpdateItemInput'; ofType: null; }; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }, { name: 'boardSeats'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'autoOpenVoting'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'votingPower'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyVotingPower'; ofType: null; }; }; defaultValue: null }]; };
    'AssemblyUpdateItemInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyUpdateItemInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'blockedIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; }; defaultValue: null }, { name: 'generated'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyItemOptionInput'; ofType: null; }; }; defaultValue: null }]; };
    'AssemblyUpdateMinutesForm': { kind: 'OBJECT'; name: 'AssemblyUpdateMinutesForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'electionSummary': { name: 'electionSummary'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'items': { name: 'items'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_discussion': { name: 'items_discussion'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'items_summary': { name: 'items_summary'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyUpdateMinutesInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyUpdateMinutesInput'; isOneOf: false; inputFields: [{ name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UpdateAssemblyMinutesInputItem'; ofType: null; }; }; }; defaultValue: null }, { name: 'electionSummary'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'AssemblyUpdateNomineeInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyUpdateNomineeInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'AssemblyUploadFileForm': { kind: 'OBJECT'; name: 'AssemblyUploadFileForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'file': { name: 'file'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AssemblyUploadFileInput': { kind: 'INPUT_OBJECT'; name: 'AssemblyUploadFileInput'; isOneOf: false; inputFields: [{ name: 'file'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }]; };
    'AssemblyVoteTodo': { kind: 'OBJECT'; name: 'AssemblyVoteTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'AssemblyStatusesOption'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'AssemblyVotingPower': { kind: 'INPUT_OBJECT'; name: 'AssemblyVotingPower'; isOneOf: false; inputFields: [{ name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shares'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'AssignPermissionForm': { kind: 'OBJECT'; name: 'AssignPermissionForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'assembly': { name: 'assembly'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardMeeting': { name: 'boardMeeting'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'capTable': { name: 'capTable'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'committees': { name: 'committees'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'esop': { name: 'esop'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'privileges': { name: 'privileges'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'profile': { name: 'profile'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'subscription': { name: 'subscription'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'userId': { name: 'userId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'AssignPermissionInput': { kind: 'INPUT_OBJECT'; name: 'AssignPermissionInput'; isOneOf: false; inputFields: [{ name: 'userId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'capTable'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'assembly'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'profile'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'esop'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'boardMeeting'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'committees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'privileges'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'subscription'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }]; };
    'AutomaticTermForm': { kind: 'OBJECT'; name: 'AutomaticTermForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'terms': { name: 'terms'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'terms_due': { name: 'terms_due'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'terms_expiryDate': { name: 'terms_expiryDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'terms_kpi': { name: 'terms_kpi'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'terms_shareCount': { name: 'terms_shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'AutomaticTermInput': { kind: 'INPUT_OBJECT'; name: 'AutomaticTermInput'; isOneOf: false; inputFields: [{ name: 'terms'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'EIGVestingTermInput'; ofType: null; }; }; defaultValue: null }]; };
    'Award': { kind: 'UNION'; name: 'Award'; fields: {}; possibleTypes: 'PhantomScheme' | 'RSU' | 'SARs' | 'StockOptions'; };
    'AwardInput': { kind: 'INPUT_OBJECT'; name: 'AwardInput'; isOneOf: false; inputFields: [{ name: 'sars'; type: { kind: 'INPUT_OBJECT'; name: 'SARsInput'; ofType: null; }; defaultValue: null }, { name: 'stockOptions'; type: { kind: 'INPUT_OBJECT'; name: 'StockOptionsInput'; ofType: null; }; defaultValue: null }, { name: 'phantom'; type: { kind: 'INPUT_OBJECT'; name: 'PhantomInput'; ofType: null; }; defaultValue: null }]; };
    'AwardType': { name: 'AwardType'; enumValues: 'SARs' | 'RSU' | 'StockOptions' | 'PhantomScheme'; };
    'AwardTypeOption': { kind: 'OBJECT'; name: 'AwardTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'AwardType'; ofType: null; } }; }; };
    'BasicComboBoxFormField': { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'kind': { name: 'kind'; type: { kind: 'ENUM'; name: 'ComboboxKind'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'BillingInformation': { kind: 'OBJECT'; name: 'BillingInformation'; fields: { 'address': { name: 'address'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'city': { name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'country': { name: 'country'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'vatNumber': { name: 'vatNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'BillingInformationUpdateForm': { kind: 'OBJECT'; name: 'BillingInformationUpdateForm'; fields: { 'address': { name: 'address'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'city': { name: 'city'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'email': { name: 'email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'vatNumber': { name: 'vatNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'zipCode': { name: 'zipCode'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'BillingInformationUpdateInput': { kind: 'INPUT_OBJECT'; name: 'BillingInformationUpdateInput'; isOneOf: false; inputFields: [{ name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'address'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'zipCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'vatNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'BillingManagement': { kind: 'OBJECT'; name: 'BillingManagement'; fields: { 'active': { name: 'active'; type: { kind: 'OBJECT'; name: 'CompanyActivation'; ofType: null; } }; 'billingInformation': { name: 'billingInformation'; type: { kind: 'OBJECT'; name: 'BillingInformation'; ofType: null; } }; 'cards': { name: 'cards'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'PaymentCard'; ofType: null; }; } }; 'informationUpdateForm': { name: 'informationUpdateForm'; type: { kind: 'OBJECT'; name: 'BillingInformationUpdateForm'; ofType: null; } }; 'invoices': { name: 'invoices'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Invoice'; ofType: null; }; } }; 'moyasarTokenizeCardForm': { name: 'moyasarTokenizeCardForm'; type: { kind: 'OBJECT'; name: 'MoyasarTokenizeCardForm'; ofType: null; } }; 'renewForm': { name: 'renewForm'; type: { kind: 'OBJECT'; name: 'SubscriptionRenewForm'; ofType: null; } }; 'subscribeForm': { name: 'subscribeForm'; type: { kind: 'OBJECT'; name: 'SubscriptionCreateForm'; ofType: null; } }; 'subscriptions': { name: 'subscriptions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanySubscription'; ofType: null; }; } }; 'upgradeForm': { name: 'upgradeForm'; type: { kind: 'OBJECT'; name: 'SubscriptionUpgradeForm'; ofType: null; } }; }; };
    'Board': { kind: 'OBJECT'; name: 'Board'; fields: { 'annualMeetings': { name: 'annualMeetings'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'circularResolution': { name: 'circularResolution'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolution'; ofType: null; } }; 'circularResolutionsPaginated': { name: 'circularResolutionsPaginated'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPaginated'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'directors': { name: 'directors'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'BoardDirector'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'meeting': { name: 'meeting'; type: { kind: 'OBJECT'; name: 'GovernanceMeeting'; ofType: null; } }; 'meetingDraftForm': { name: 'meetingDraftForm'; type: { kind: 'OBJECT'; name: 'BoardMeetingDraftForm'; ofType: null; } }; 'meetingsPaginated': { name: 'meetingsPaginated'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPaginated'; ofType: null; } }; 'observers': { name: 'observers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'BoardObserver'; ofType: null; }; } }; 'quorum': { name: 'quorum'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'GovernanceRelationOption'; ofType: null; } }; 'resolutionDraftForm': { name: 'resolutionDraftForm'; type: { kind: 'OBJECT'; name: 'BoardResolutionDraftForm'; ofType: null; } }; 'secretary': { name: 'secretary'; type: { kind: 'OBJECT'; name: 'BoardSecretary'; ofType: null; } }; 'setupForm': { name: 'setupForm'; type: { kind: 'OBJECT'; name: 'BoardSetupForm'; ofType: null; } }; 'taskAssigner': { name: 'taskAssigner'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'BoardDirector'; ofType: null; }; } }; 'taskAssigners': { name: 'taskAssigners'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'BoardDirector'; ofType: null; }; } }; 'terms': { name: 'terms'; type: { kind: 'OBJECT'; name: 'GovernanceTerms'; ofType: null; } }; }; };
    'BoardDirector': { kind: 'OBJECT'; name: 'BoardDirector'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'BoardDirectorTypeOption'; ofType: null; } }; }; };
    'BoardDirectorInput': { kind: 'INPUT_OBJECT'; name: 'BoardDirectorInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'ENUM'; name: 'BoardDirectorType'; ofType: null; }; defaultValue: null }, { name: 'nationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'mobileNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'BoardDirectorType': { name: 'BoardDirectorType'; enumValues: 'CHAIR' | 'VICE_CHAIR' | 'MANAGING' | 'EXECUTIVE' | 'NON_EXECUTIVE' | 'INDEPENDENT'; };
    'BoardDirectorTypeOption': { kind: 'OBJECT'; name: 'BoardDirectorTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'BoardDirectorType'; ofType: null; } }; }; };
    'BoardMeetingDraftForm': { kind: 'OBJECT'; name: 'BoardMeetingDraftForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'items': { name: 'items'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_options': { name: 'items_options'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_options_text': { name: 'items_options_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'items_text': { name: 'items_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingPassword': { name: 'meetingPassword'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingUrl': { name: 'meetingUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'onlineMeeting': { name: 'onlineMeeting'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'onlineMeeting_ebanaRoom': { name: 'onlineMeeting_ebanaRoom'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'onlineMeeting_ebanaRoom_ignored': { name: 'onlineMeeting_ebanaRoom_ignored'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'onlineMeeting_external': { name: 'onlineMeeting_external'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'onlineMeeting_external_password': { name: 'onlineMeeting_external_password'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'onlineMeeting_external_url': { name: 'onlineMeeting_external_url'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'proxies': { name: 'proxies'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'proxies_principal': { name: 'proxies_principal'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'proxies_proxy': { name: 'proxies_proxy'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'BoardMeetingDraftInput': { kind: 'INPUT_OBJECT'; name: 'BoardMeetingDraftInput'; isOneOf: false; inputFields: [{ name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'date'; type: { kind: 'SCALAR'; name: 'OffsetDateTime'; ofType: null; }; defaultValue: null }, { name: 'meetingUrl'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'meetingPassword'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'onlineMeeting'; type: { kind: 'INPUT_OBJECT'; name: 'OnlineMeetingInput'; ofType: null; }; defaultValue: null }, { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'BoardMeetingItemInput'; ofType: null; }; }; defaultValue: null }, { name: 'proxies'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'BoardMeetingProxyInput'; ofType: null; }; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; }; defaultValue: null }]; };
    'BoardMeetingItemInput': { kind: 'INPUT_OBJECT'; name: 'BoardMeetingItemInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'BoardMeetingItemOptionInput'; ofType: null; }; }; defaultValue: null }]; };
    'BoardMeetingItemOptionInput': { kind: 'INPUT_OBJECT'; name: 'BoardMeetingItemOptionInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'BoardMeetingProxyInput': { kind: 'INPUT_OBJECT'; name: 'BoardMeetingProxyInput'; isOneOf: false; inputFields: [{ name: 'principal'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'proxy'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'BoardMeetingVoteTodo': { kind: 'OBJECT'; name: 'BoardMeetingVoteTodo'; fields: { 'boardId': { name: 'boardId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingStatusOption'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'BoardObserver': { kind: 'OBJECT'; name: 'BoardObserver'; fields: { 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'mobileNumber': { name: 'mobileNumber'; type: { kind: 'OBJECT'; name: 'PhoneNumber'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'BoardObserverInput': { kind: 'INPUT_OBJECT'; name: 'BoardObserverInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'BoardPayload': { kind: 'OBJECT'; name: 'BoardPayload'; fields: { 'board': { name: 'board'; type: { kind: 'OBJECT'; name: 'Board'; ofType: null; } }; }; };
    'BoardResolutionDraftForm': { kind: 'OBJECT'; name: 'BoardResolutionDraftForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'quorum': { name: 'quorum'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'resolutionNumber': { name: 'resolutionNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'votingDeadline': { name: 'votingDeadline'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; }; };
    'BoardResolutionDraftInput': { kind: 'INPUT_OBJECT'; name: 'BoardResolutionDraftInput'; isOneOf: false; inputFields: [{ name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'resolutionNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'quorum'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'votingDeadline'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }]; };
    'BoardResolutionVoteTodo': { kind: 'OBJECT'; name: 'BoardResolutionVoteTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'deadline': { name: 'deadline'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'BoardSecretary': { kind: 'OBJECT'; name: 'BoardSecretary'; fields: { 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'mobileNumber': { name: 'mobileNumber'; type: { kind: 'OBJECT'; name: 'PhoneNumber'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'BoardSetupForm': { kind: 'OBJECT'; name: 'BoardSetupForm'; fields: { 'annualMeetings': { name: 'annualMeetings'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'directors': { name: 'directors'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'directors_email': { name: 'directors_email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'directors_id': { name: 'directors_id'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'directors_id_type': { name: 'directors_id_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'directors_id_value': { name: 'directors_id_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'directors_mobileNumber': { name: 'directors_mobileNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'directors_name': { name: 'directors_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'directors_nationality': { name: 'directors_nationality'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'directors_type': { name: 'directors_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'observers': { name: 'observers'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'observers_email': { name: 'observers_email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'observers_name': { name: 'observers_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'quorum': { name: 'quorum'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'secretaryEmail': { name: 'secretaryEmail'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secretaryId': { name: 'secretaryId'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'secretaryId_type': { name: 'secretaryId_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'secretaryId_value': { name: 'secretaryId_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secretaryName': { name: 'secretaryName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'taskAssigners': { name: 'taskAssigners'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'taskAssigners_id': { name: 'taskAssigners_id'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'BoardSetupInput': { kind: 'INPUT_OBJECT'; name: 'BoardSetupInput'; isOneOf: false; inputFields: [{ name: 'directors'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'BoardDirectorInput'; ofType: null; }; }; defaultValue: null }, { name: 'quorum'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'annualMeetings'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'secretaryId'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'secretaryName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'secretaryEmail'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'observers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'BoardObserverInput'; ofType: null; }; }; defaultValue: null }, { name: 'taskAssigners'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'BoardTaskAssignerInput'; ofType: null; }; }; defaultValue: null }]; };
    'BoardSetupPayload': { kind: 'OBJECT'; name: 'BoardSetupPayload'; fields: { 'board': { name: 'board'; type: { kind: 'OBJECT'; name: 'Board'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'BoardTaskAssignerInput': { kind: 'INPUT_OBJECT'; name: 'BoardTaskAssignerInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'Boolean': unknown;
    'BooleanFormField': { kind: 'OBJECT'; name: 'BooleanFormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'BusinessType': { name: 'BusinessType'; enumValues: 'CJSC' | 'ASSOCIATION' | 'LLC' | 'SJSC'; };
    'BusinessTypeOption': { kind: 'OBJECT'; name: 'BusinessTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'BusinessType'; ofType: null; } }; }; };
    'Calendar': { kind: 'OBJECT'; name: 'Calendar'; fields: { 'events': { name: 'events'; type: { kind: 'LIST'; name: never; ofType: { kind: 'UNION'; name: 'CalendarEvent'; ofType: null; }; } }; }; };
    'CalendarEvent': { kind: 'UNION'; name: 'CalendarEvent'; fields: {}; possibleTypes: 'GovernanceMeetingCalendarEvent'; };
    'ComboBoxCompany': { kind: 'OBJECT'; name: 'ComboBoxCompany'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'kind': { name: 'kind'; type: { kind: 'ENUM'; name: 'ComboboxKind'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Company'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'ComboBoxCompanyPlan': { kind: 'OBJECT'; name: 'ComboBoxCompanyPlan'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'kind': { name: 'kind'; type: { kind: 'ENUM'; name: 'ComboboxKind'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyPlan'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'ComboBoxCountry': { kind: 'OBJECT'; name: 'ComboBoxCountry'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'kind': { name: 'kind'; type: { kind: 'ENUM'; name: 'ComboboxKind'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'ComboBoxFormField': { kind: 'INTERFACE'; name: 'ComboBoxFormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'kind': { name: 'kind'; type: { kind: 'ENUM'; name: 'ComboboxKind'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; possibleTypes: 'BasicComboBoxFormField' | 'ComboBoxCompany' | 'ComboBoxCompanyPlan' | 'ComboBoxCountry' | 'ComboBoxShareClass' | 'ComboBoxStringOption'; };
    'ComboBoxShareClass': { kind: 'OBJECT'; name: 'ComboBoxShareClass'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'kind': { name: 'kind'; type: { kind: 'ENUM'; name: 'ComboboxKind'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'ComboBoxStringOption': { kind: 'OBJECT'; name: 'ComboBoxStringOption'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'kind': { name: 'kind'; type: { kind: 'ENUM'; name: 'ComboboxKind'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'ComboboxKind': { name: 'ComboboxKind'; enumValues: 'LIMITED' | 'CREATABLE' | 'FREETEXT'; };
    'CommercialRegistration': { kind: 'OBJECT'; name: 'CommercialRegistration'; fields: { 'businessType': { name: 'businessType'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'capital': { name: 'capital'; type: { kind: 'OBJECT'; name: 'CommercialRegistrationCapital'; ofType: null; } }; 'capitalAmount': { name: 'capitalAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'chair': { name: 'chair'; type: { kind: 'OBJECT'; name: 'CommercialRegistrationParty'; ofType: null; } }; 'deputyChair': { name: 'deputyChair'; type: { kind: 'OBJECT'; name: 'CommercialRegistrationParty'; ofType: null; } }; 'directors': { name: 'directors'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CommercialRegistrationParty'; ofType: null; }; } }; 'entityType': { name: 'entityType'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'issueDate': { name: 'issueDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'managers': { name: 'managers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CommercialRegistrationParty'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'number': { name: 'number'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'partners': { name: 'partners'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CommercialRegistrationPartner'; ofType: null; }; } }; 'startDate': { name: 'startDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'topAuthority': { name: 'topAuthority'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CommercialRegistrationTopAuthority'; ofType: null; }; } }; }; };
    'CommercialRegistrationCapital': { kind: 'OBJECT'; name: 'CommercialRegistrationCapital'; fields: { 'issuedAmount': { name: 'issuedAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'issuedShares': { name: 'issuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'parValue': { name: 'parValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; }; };
    'CommercialRegistrationLookup': { kind: 'OBJECT'; name: 'CommercialRegistrationLookup'; fields: { 'commercialRegistration': { name: 'commercialRegistration'; type: { kind: 'OBJECT'; name: 'CommercialRegistration'; ofType: null; } }; 'plans': { name: 'plans'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; } }; 'registerForm': { name: 'registerForm'; type: { kind: 'OBJECT'; name: 'CompanyRegistrationForm'; ofType: null; } }; 'topAuthority': { name: 'topAuthority'; type: { kind: 'OBJECT'; name: 'CommercialRegistrationTopAuthority'; ofType: null; } }; }; };
    'CommercialRegistrationLookupForm': { kind: 'OBJECT'; name: 'CommercialRegistrationLookupForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'crNumber': { name: 'crNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'CommercialRegistrationLookupInput': { kind: 'INPUT_OBJECT'; name: 'CommercialRegistrationLookupInput'; isOneOf: false; inputFields: [{ name: 'crNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CommercialRegistrationPartner': { kind: 'OBJECT'; name: 'CommercialRegistrationPartner'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'CommercialRegistrationParty': { kind: 'OBJECT'; name: 'CommercialRegistrationParty'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'CommercialRegistrationTopAuthority': { kind: 'OBJECT'; name: 'CommercialRegistrationTopAuthority'; fields: { 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'mobile': { name: 'mobile'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'Committee': { kind: 'OBJECT'; name: 'Committee'; fields: { 'annualMeetings': { name: 'annualMeetings'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'circularResolution': { name: 'circularResolution'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolution'; ofType: null; } }; 'circularResolutionsPaginated': { name: 'circularResolutionsPaginated'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPaginated'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'meeting': { name: 'meeting'; type: { kind: 'OBJECT'; name: 'GovernanceMeeting'; ofType: null; } }; 'meetingDraftForm': { name: 'meetingDraftForm'; type: { kind: 'OBJECT'; name: 'CommitteeMeetingDraftForm'; ofType: null; } }; 'meetingsPaginated': { name: 'meetingsPaginated'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPaginated'; ofType: null; } }; 'members': { name: 'members'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CommitteeMember'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'observers': { name: 'observers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CommitteeObserver'; ofType: null; }; } }; 'quorum': { name: 'quorum'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'GovernanceRelationOption'; ofType: null; } }; 'resolutionDraftForm': { name: 'resolutionDraftForm'; type: { kind: 'OBJECT'; name: 'CommitteeCircularResolutionDraftForm'; ofType: null; } }; 'secretary': { name: 'secretary'; type: { kind: 'OBJECT'; name: 'CommitteeSecretary'; ofType: null; } }; 'setupForm': { name: 'setupForm'; type: { kind: 'OBJECT'; name: 'CommitteeSetupForm'; ofType: null; } }; 'taskAssigner': { name: 'taskAssigner'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CommitteeMember'; ofType: null; }; } }; 'taskAssigners': { name: 'taskAssigners'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CommitteeMember'; ofType: null; }; } }; 'terms': { name: 'terms'; type: { kind: 'OBJECT'; name: 'GovernanceTerms'; ofType: null; } }; }; };
    'CommitteeCircularResolutionDraftForm': { kind: 'OBJECT'; name: 'CommitteeCircularResolutionDraftForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'quorum': { name: 'quorum'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'resolutionNumber': { name: 'resolutionNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'votingDeadline': { name: 'votingDeadline'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; }; };
    'CommitteeCircularResolutionDraftInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeCircularResolutionDraftInput'; isOneOf: false; inputFields: [{ name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'resolutionNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'quorum'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'votingDeadline'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }]; };
    'CommitteeMeetingDraftForm': { kind: 'OBJECT'; name: 'CommitteeMeetingDraftForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'items': { name: 'items'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_options': { name: 'items_options'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_options_text': { name: 'items_options_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'items_text': { name: 'items_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingPassword': { name: 'meetingPassword'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'meetingUrl': { name: 'meetingUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'onlineMeeting': { name: 'onlineMeeting'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'onlineMeeting_ebanaRoom': { name: 'onlineMeeting_ebanaRoom'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'onlineMeeting_ebanaRoom_ignored': { name: 'onlineMeeting_ebanaRoom_ignored'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'onlineMeeting_external': { name: 'onlineMeeting_external'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'onlineMeeting_external_password': { name: 'onlineMeeting_external_password'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'onlineMeeting_external_url': { name: 'onlineMeeting_external_url'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'proxies': { name: 'proxies'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'proxies_principal': { name: 'proxies_principal'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'proxies_proxy': { name: 'proxies_proxy'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'CommitteeMeetingDraftInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeMeetingDraftInput'; isOneOf: false; inputFields: [{ name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'date'; type: { kind: 'SCALAR'; name: 'OffsetDateTime'; ofType: null; }; defaultValue: null }, { name: 'meetingUrl'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'meetingPassword'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'onlineMeeting'; type: { kind: 'INPUT_OBJECT'; name: 'OnlineMeetingInput'; ofType: null; }; defaultValue: null }, { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CommitteeMeetingItemInput'; ofType: null; }; }; defaultValue: null }, { name: 'proxies'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CommitteeMeetingProxyInput'; ofType: null; }; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; }; defaultValue: null }]; };
    'CommitteeMeetingItemInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeMeetingItemInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CommitteeMeetingItemOptionInput'; ofType: null; }; }; defaultValue: null }]; };
    'CommitteeMeetingItemOptionInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeMeetingItemOptionInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CommitteeMeetingProxyInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeMeetingProxyInput'; isOneOf: false; inputFields: [{ name: 'principal'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'proxy'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CommitteeMeetingVoteTodo': { kind: 'OBJECT'; name: 'CommitteeMeetingVoteTodo'; fields: { 'committeeId': { name: 'committeeId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingStatusOption'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'CommitteeMember': { kind: 'OBJECT'; name: 'CommitteeMember'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'CommitteeMemberTypeOption'; ofType: null; } }; }; };
    'CommitteeMemberInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeMemberInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'ENUM'; name: 'CommitteeMemberType'; ofType: null; }; defaultValue: null }, { name: 'nationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'mobileNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CommitteeMemberType': { name: 'CommitteeMemberType'; enumValues: 'CHAIR' | 'VICE_CHAIR' | 'MEMBER' | 'INDEPENDENT'; };
    'CommitteeMemberTypeOption': { kind: 'OBJECT'; name: 'CommitteeMemberTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'CommitteeMemberType'; ofType: null; } }; }; };
    'CommitteeObserver': { kind: 'OBJECT'; name: 'CommitteeObserver'; fields: { 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'mobileNumber': { name: 'mobileNumber'; type: { kind: 'OBJECT'; name: 'PhoneNumber'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'CommitteeObserverInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeObserverInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CommitteePayload': { kind: 'OBJECT'; name: 'CommitteePayload'; fields: { 'committee': { name: 'committee'; type: { kind: 'OBJECT'; name: 'Committee'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'CommitteeSecretary': { kind: 'OBJECT'; name: 'CommitteeSecretary'; fields: { 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'mobileNumber': { name: 'mobileNumber'; type: { kind: 'OBJECT'; name: 'PhoneNumber'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'CommitteeSetupForm': { kind: 'OBJECT'; name: 'CommitteeSetupForm'; fields: { 'annualMeetings': { name: 'annualMeetings'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'members': { name: 'members'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'members_email': { name: 'members_email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'members_id': { name: 'members_id'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'members_id_type': { name: 'members_id_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'members_id_value': { name: 'members_id_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'members_mobileNumber': { name: 'members_mobileNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'members_name': { name: 'members_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'members_nationality': { name: 'members_nationality'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'members_type': { name: 'members_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'name_ar': { name: 'name_ar'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'name_en': { name: 'name_en'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'observers': { name: 'observers'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'observers_email': { name: 'observers_email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'observers_name': { name: 'observers_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'quorum': { name: 'quorum'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'secretaryEmail': { name: 'secretaryEmail'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secretaryId': { name: 'secretaryId'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'secretaryId_type': { name: 'secretaryId_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'secretaryId_value': { name: 'secretaryId_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secretaryName': { name: 'secretaryName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'taskAssigners': { name: 'taskAssigners'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'taskAssigners_id': { name: 'taskAssigners_id'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'CommitteeSetupInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeSetupInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'I18nStringInput'; ofType: null; }; defaultValue: null }, { name: 'members'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CommitteeMemberInput'; ofType: null; }; }; defaultValue: null }, { name: 'quorum'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'annualMeetings'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'secretaryId'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'secretaryName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'secretaryEmail'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'observers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CommitteeObserverInput'; ofType: null; }; }; defaultValue: null }, { name: 'taskAssigners'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CommitteeTaskAssignerInput'; ofType: null; }; }; defaultValue: null }]; };
    'CommitteeTaskAssignerInput': { kind: 'INPUT_OBJECT'; name: 'CommitteeTaskAssignerInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'Company': { kind: 'OBJECT'; name: 'Company'; fields: { 'address': { name: 'address'; type: { kind: 'OBJECT'; name: 'CompanyAddress'; ofType: null; } }; 'bio': { name: 'bio'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'board': { name: 'board'; type: { kind: 'OBJECT'; name: 'Board'; ofType: null; } }; 'capital': { name: 'capital'; type: { kind: 'OBJECT'; name: 'CompanyCapital'; ofType: null; } }; 'chair': { name: 'chair'; type: { kind: 'OBJECT'; name: 'CompanyParty'; ofType: null; } }; 'companyVideo': { name: 'companyVideo'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'cr': { name: 'cr'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'deputy': { name: 'deputy'; type: { kind: 'OBJECT'; name: 'CompanyParty'; ofType: null; } }; 'directGiftCreateForm': { name: 'directGiftCreateForm'; type: { kind: 'OBJECT'; name: 'DirectGiftCreateForm'; ofType: null; } }; 'directSellCreateForm': { name: 'directSellCreateForm'; type: { kind: 'OBJECT'; name: 'DirectSellCreateForm'; ofType: null; } }; 'directTradeFee': { name: 'directTradeFee'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'directors': { name: 'directors'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyParty'; ofType: null; }; } }; 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'equity': { name: 'equity'; type: { kind: 'INTERFACE'; name: 'Equity'; ofType: null; } }; 'executives': { name: 'executives'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyExecutive'; ofType: null; }; } }; 'featureFlags': { name: 'featureFlags'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'FeatureFlag'; ofType: null; }; } }; 'gicsCode': { name: 'gicsCode'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'investorRelationsManager': { name: 'investorRelationsManager'; type: { kind: 'ENUM'; name: 'InvestorRelationsManager'; ofType: null; } }; 'logo': { name: 'logo'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'managers': { name: 'managers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyParty'; ofType: null; }; } }; 'phone': { name: 'phone'; type: { kind: 'OBJECT'; name: 'PhoneNumber'; ofType: null; } }; 'plan': { name: 'plan'; type: { kind: 'ENUM'; name: 'CompanyPlanKey'; ofType: null; } }; 'pressRelease': { name: 'pressRelease'; type: { kind: 'OBJECT'; name: 'PressRelease'; ofType: null; } }; 'pressReleasesPaginated': { name: 'pressReleasesPaginated'; type: { kind: 'OBJECT'; name: 'PressReleasePaginated'; ofType: null; } }; 'register': { name: 'register'; type: { kind: 'OBJECT'; name: 'EquityPositionRegister'; ofType: null; } }; 'registration': { name: 'registration'; type: { kind: 'OBJECT'; name: 'CompanyRegistration'; ofType: null; } }; 'sector': { name: 'sector'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'socialAccounts': { name: 'socialAccounts'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'SocialAccount'; ofType: null; }; } }; 'stamp': { name: 'stamp'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'tradeName': { name: 'tradeName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tradingPolicy': { name: 'tradingPolicy'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'BusinessTypeOption'; ofType: null; } }; 'website': { name: 'website'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; }; };
    'CompanyActivation': { kind: 'OBJECT'; name: 'CompanyActivation'; fields: { 'addons': { name: 'addons'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyActivationAddonUsage'; ofType: null; }; } }; 'end': { name: 'end'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'invoice': { name: 'invoice'; type: { kind: 'OBJECT'; name: 'Invoice'; ofType: null; } }; 'plan': { name: 'plan'; type: { kind: 'OBJECT'; name: 'CompanyPlan'; ofType: null; } }; 'start': { name: 'start'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; }; };
    'CompanyActivationAddonUsage': { kind: 'OBJECT'; name: 'CompanyActivationAddonUsage'; fields: { 'additional': { name: 'additional'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'addon': { name: 'addon'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'price': { name: 'price'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'purchaseForm': { name: 'purchaseForm'; type: { kind: 'OBJECT'; name: 'AddonPurchaseForm'; ofType: null; } }; }; };
    'CompanyAddress': { kind: 'OBJECT'; name: 'CompanyAddress'; fields: { 'city': { name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'district': { name: 'district'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'streetName': { name: 'streetName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'zipCode': { name: 'zipCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'CompanyAuthorizeAgentForm': { kind: 'OBJECT'; name: 'CompanyAuthorizeAgentForm'; fields: { 'agencyNumber': { name: 'agencyNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'agentId': { name: 'agentId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'dateOfBirth': { name: 'dateOfBirth'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'dateOfIssuance': { name: 'dateOfIssuance'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'equityId': { name: 'equityId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'expirationDate': { name: 'expirationDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'CompanyAuthorizeAgentInput': { kind: 'INPUT_OBJECT'; name: 'CompanyAuthorizeAgentInput'; isOneOf: false; inputFields: [{ name: 'equityId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'agentId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'nationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'dateOfBirth'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'agencyNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'dateOfIssuance'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'expirationDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }]; };
    'CompanyCapital': { kind: 'OBJECT'; name: 'CompanyCapital'; fields: { 'indicativePrice': { name: 'indicativePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'parValue': { name: 'parValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalAmount': { name: 'totalAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalShares': { name: 'totalShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'CompanyDelegation': { kind: 'OBJECT'; name: 'CompanyDelegation'; fields: { 'companies': { name: 'companies'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'DelegatedCompany'; ofType: null; }; } }; 'delegationRevokeForm': { name: 'delegationRevokeForm'; type: { kind: 'OBJECT'; name: 'CompanyDelegationRevokeForm'; ofType: null; } }; 'delegationUpdateForm': { name: 'delegationUpdateForm'; type: { kind: 'OBJECT'; name: 'CompanyDelegationUpdateForm'; ofType: null; } }; 'document': { name: 'document'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'documents': { name: 'documents'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'userId': { name: 'userId'; type: { kind: 'OBJECT'; name: 'NaturalPersonIdentifier'; ofType: null; } }; }; };
    'CompanyDelegationCreateForm': { kind: 'OBJECT'; name: 'CompanyDelegationCreateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'companies': { name: 'companies'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'companies_assembly': { name: 'companies_assembly'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'companies_companyId': { name: 'companies_companyId'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'companies_end': { name: 'companies_end'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'companies_start': { name: 'companies_start'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'documents': { name: 'documents'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'legalCapacity': { name: 'legalCapacity'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'mobileNumber': { name: 'mobileNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'userId': { name: 'userId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'CompanyDelegationCreateInput': { kind: 'INPUT_OBJECT'; name: 'CompanyDelegationCreateInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'userId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'mobileNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'legalCapacity'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'companies'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CompanyDelegationInput'; ofType: null; }; }; defaultValue: null }, { name: 'documents'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }]; };
    'CompanyDelegationInput': { kind: 'INPUT_OBJECT'; name: 'CompanyDelegationInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'start'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'end'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'assembly'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }]; };
    'CompanyDelegationRevokeForm': { kind: 'OBJECT'; name: 'CompanyDelegationRevokeForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'userId': { name: 'userId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'CompanyDelegationRevokeInput': { kind: 'INPUT_OBJECT'; name: 'CompanyDelegationRevokeInput'; isOneOf: false; inputFields: [{ name: 'userId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CompanyDelegationUpdateForm': { kind: 'OBJECT'; name: 'CompanyDelegationUpdateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'companies': { name: 'companies'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'companies_assembly': { name: 'companies_assembly'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'companies_companyId': { name: 'companies_companyId'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'companies_end': { name: 'companies_end'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'companies_start': { name: 'companies_start'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'userId': { name: 'userId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'CompanyDelegationUpdateInput': { kind: 'INPUT_OBJECT'; name: 'CompanyDelegationUpdateInput'; isOneOf: false; inputFields: [{ name: 'userId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'companies'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CompanyDelegationInput'; ofType: null; }; }; defaultValue: null }]; };
    'CompanyDismissInsightForm': { kind: 'OBJECT'; name: 'CompanyDismissInsightForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'CompanyDismissInsightInput': { kind: 'INPUT_OBJECT'; name: 'CompanyDismissInsightInput'; isOneOf: false; inputFields: [{ name: 'insight'; type: { kind: 'ENUM'; name: 'DashboardInsight'; ofType: null; }; defaultValue: null }]; };
    'CompanyEquity': { kind: 'OBJECT'; name: 'CompanyEquity'; fields: { 'agent': { name: 'agent'; type: { kind: 'OBJECT'; name: 'EquityAgent'; ofType: null; } }; 'authorizeAgentForm': { name: 'authorizeAgentForm'; type: { kind: 'OBJECT'; name: 'CompanyAuthorizeAgentForm'; ofType: null; } }; 'authorizeAgentForm_agentNationality': { name: 'authorizeAgentForm_agentNationality'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; } }; 'averageCostPerShares': { name: 'averageCostPerShares'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'certificate': { name: 'certificate'; type: { kind: 'OBJECT'; name: 'EquityCertificate'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'marketValue': { name: 'marketValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'policyAccepted': { name: 'policyAccepted'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'position': { name: 'position'; type: { kind: 'OBJECT'; name: 'EquityPosition'; ofType: null; } }; 'positions': { name: 'positions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityPosition'; ofType: null; }; } }; 'profitAndLoss': { name: 'profitAndLoss'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'profitAndLossPercent': { name: 'profitAndLossPercent'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'recentRecipients': { name: 'recentRecipients'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; }; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'terminateAgencyForm': { name: 'terminateAgencyForm'; type: { kind: 'OBJECT'; name: 'CompanyTerminateAgencyForm'; ofType: null; } }; 'totalCost': { name: 'totalCost'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalOwnership': { name: 'totalOwnership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'totalShares': { name: 'totalShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'tradingPolicy': { name: 'tradingPolicy'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; }; };
    'CompanyExecutive': { kind: 'OBJECT'; name: 'CompanyExecutive'; fields: { 'bio': { name: 'bio'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'photo': { name: 'photo'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'position': { name: 'position'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'socialAccounts': { name: 'socialAccounts'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'SocialAccount'; ofType: null; }; } }; }; };
    'CompanyGovernanceSummary': { kind: 'OBJECT'; name: 'CompanyGovernanceSummary'; fields: { 'totalAssemblyMeetings': { name: 'totalAssemblyMeetings'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'totalBoardMeetings': { name: 'totalBoardMeetings'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'totalCommitteeMeetings': { name: 'totalCommitteeMeetings'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'yearAssemblyMeetings': { name: 'yearAssemblyMeetings'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'yearBoardMeetings': { name: 'yearBoardMeetings'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'yearCommitteeMeetings': { name: 'yearCommitteeMeetings'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'CompanyManagement': { kind: 'OBJECT'; name: 'CompanyManagement'; fields: { 'address': { name: 'address'; type: { kind: 'OBJECT'; name: 'CompanyAddress'; ofType: null; } }; 'assembliesPaginated': { name: 'assembliesPaginated'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPaginated'; ofType: null; } }; 'assembly': { name: 'assembly'; type: { kind: 'OBJECT'; name: 'AssemblyManagement'; ofType: null; } }; 'assemblyCreateForm': { name: 'assemblyCreateForm'; type: { kind: 'OBJECT'; name: 'AssemblyCreateForm'; ofType: null; } }; 'assembly_shareholder_options': { name: 'assembly_shareholder_options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Shareholder'; ofType: null; }; } }; 'assignPermissionForm': { name: 'assignPermissionForm'; type: { kind: 'OBJECT'; name: 'AssignPermissionForm'; ofType: null; } }; 'billing': { name: 'billing'; type: { kind: 'OBJECT'; name: 'BillingManagement'; ofType: null; } }; 'bio': { name: 'bio'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'board': { name: 'board'; type: { kind: 'OBJECT'; name: 'Board'; ofType: null; } }; 'boardSetupForm': { name: 'boardSetupForm'; type: { kind: 'OBJECT'; name: 'BoardSetupForm'; ofType: null; } }; 'calendar': { name: 'calendar'; type: { kind: 'OBJECT'; name: 'Calendar'; ofType: null; } }; 'capital': { name: 'capital'; type: { kind: 'OBJECT'; name: 'CompanyCapital'; ofType: null; } }; 'chair': { name: 'chair'; type: { kind: 'OBJECT'; name: 'CompanyParty'; ofType: null; } }; 'committee': { name: 'committee'; type: { kind: 'OBJECT'; name: 'Committee'; ofType: null; } }; 'committeeSetupForm': { name: 'committeeSetupForm'; type: { kind: 'OBJECT'; name: 'CommitteeSetupForm'; ofType: null; } }; 'committees': { name: 'committees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Committee'; ofType: null; }; } }; 'companyVideo': { name: 'companyVideo'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'cr': { name: 'cr'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'delegationCreateForm': { name: 'delegationCreateForm'; type: { kind: 'OBJECT'; name: 'CompanyDelegationCreateForm'; ofType: null; } }; 'delegations': { name: 'delegations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyDelegation'; ofType: null; }; } }; 'deputy': { name: 'deputy'; type: { kind: 'OBJECT'; name: 'CompanyParty'; ofType: null; } }; 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTradeManagement'; ofType: null; } }; 'directTradesPaginated': { name: 'directTradesPaginated'; type: { kind: 'OBJECT'; name: 'DirectTradeManagementPaginated'; ofType: null; } }; 'directTradesXlsx': { name: 'directTradesXlsx'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'directors': { name: 'directors'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyParty'; ofType: null; }; } }; 'eig': { name: 'eig'; type: { kind: 'OBJECT'; name: 'EIGManagement'; ofType: null; } }; 'eigCreateForm': { name: 'eigCreateForm'; type: { kind: 'OBJECT'; name: 'EIGCreateForm'; ofType: null; } }; 'eigCreateForm_eip': { name: 'eigCreateForm_eip'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; } }; 'eigCreateForm_granteeNationality': { name: 'eigCreateForm_granteeNationality'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; } }; 'eigPaginated': { name: 'eigPaginated'; type: { kind: 'OBJECT'; name: 'EIGManagementPaginated'; ofType: null; } }; 'eigPreviewAutomaticVestingSchedule': { name: 'eigPreviewAutomaticVestingSchedule'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'VestingTermManagement'; ofType: null; }; } }; 'eip': { name: 'eip'; type: { kind: 'OBJECT'; name: 'EIPManagement'; ofType: null; } }; 'eipCreateForm': { name: 'eipCreateForm'; type: { kind: 'OBJECT'; name: 'EIPCreateForm'; ofType: null; } }; 'eipPaginated': { name: 'eipPaginated'; type: { kind: 'OBJECT'; name: 'EIPManagementPaginated'; ofType: null; } }; 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'equities': { name: 'equities'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyEquity'; ofType: null; }; } }; 'equity': { name: 'equity'; type: { kind: 'OBJECT'; name: 'CompanyEquity'; ofType: null; } }; 'equityIncentiveExecutiveSummary': { name: 'equityIncentiveExecutiveSummary'; type: { kind: 'OBJECT'; name: 'EquityIncentiveExecutiveSummary'; ofType: null; } }; 'equityIncentiveReports': { name: 'equityIncentiveReports'; type: { kind: 'OBJECT'; name: 'EquityIncentiveCompanyReports'; ofType: null; } }; 'executives': { name: 'executives'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyExecutive'; ofType: null; }; } }; 'fullyDiluted': { name: 'fullyDiluted'; type: { kind: 'OBJECT'; name: 'FullyDilutedRegister'; ofType: null; } }; 'gicsCode': { name: 'gicsCode'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; 'governanceSummary': { name: 'governanceSummary'; type: { kind: 'OBJECT'; name: 'CompanyGovernanceSummary'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'investorRelationsManager': { name: 'investorRelationsManager'; type: { kind: 'ENUM'; name: 'InvestorRelationsManager'; ofType: null; } }; 'invitePermissionForm': { name: 'invitePermissionForm'; type: { kind: 'OBJECT'; name: 'InvitePermissionForm'; ofType: null; } }; 'logo': { name: 'logo'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'managers': { name: 'managers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyParty'; ofType: null; }; } }; 'onboarding': { name: 'onboarding'; type: { kind: 'OBJECT'; name: 'CompanyOnboarding'; ofType: null; } }; 'permissionAssignments': { name: 'permissionAssignments'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionAssignment'; ofType: null; }; }; } }; 'phone': { name: 'phone'; type: { kind: 'OBJECT'; name: 'PhoneNumber'; ofType: null; } }; 'plan': { name: 'plan'; type: { kind: 'ENUM'; name: 'CompanyPlanKey'; ofType: null; } }; 'pressRelease': { name: 'pressRelease'; type: { kind: 'OBJECT'; name: 'PressRelease'; ofType: null; } }; 'pressReleaseCreateForm': { name: 'pressReleaseCreateForm'; type: { kind: 'OBJECT'; name: 'PressReleaseCreateForm'; ofType: null; } }; 'pressReleasesPaginated': { name: 'pressReleasesPaginated'; type: { kind: 'OBJECT'; name: 'PressReleasePaginated'; ofType: null; } }; 'register': { name: 'register'; type: { kind: 'OBJECT'; name: 'ShareholderRegister'; ofType: null; } }; 'registration': { name: 'registration'; type: { kind: 'OBJECT'; name: 'CompanyRegistration'; ofType: null; } }; 'revokePermissionsForm': { name: 'revokePermissionsForm'; type: { kind: 'OBJECT'; name: 'RevokePermissionForm'; ofType: null; } }; 'safe': { name: 'safe'; type: { kind: 'OBJECT'; name: 'Safe'; ofType: null; } }; 'safeCreateForm': { name: 'safeCreateForm'; type: { kind: 'OBJECT'; name: 'SafeCreateForm'; ofType: null; } }; 'safesPaginated': { name: 'safesPaginated'; type: { kind: 'OBJECT'; name: 'SafePaginated'; ofType: null; } }; 'setupCapTableForm': { name: 'setupCapTableForm'; type: { kind: 'OBJECT'; name: 'SetupCapTableForm'; ofType: null; } }; 'setupCapTableForm_countryCodes': { name: 'setupCapTableForm_countryCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'smartInsight': { name: 'smartInsight'; type: { kind: 'OBJECT'; name: 'SmartInsight'; ofType: null; } }; 'socialAccounts': { name: 'socialAccounts'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'SocialAccount'; ofType: null; }; } }; 'stamp': { name: 'stamp'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'todos': { name: 'todos'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INTERFACE'; name: 'Todo'; ofType: null; }; } }; 'tradeName': { name: 'tradeName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'treasuryShareCount': { name: 'treasuryShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'BusinessTypeOption'; ofType: null; } }; 'upcomingMeetings': { name: 'upcomingMeetings'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeeting'; ofType: null; }; } }; 'updateForm': { name: 'updateForm'; type: { kind: 'OBJECT'; name: 'UpdateCompanyForm'; ofType: null; } }; 'updateForm_gicsIndustryGroups': { name: 'updateForm_gicsIndustryGroups'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; 'updateForm_gicsSectors': { name: 'updateForm_gicsSectors'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; 'website': { name: 'website'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; }; };
    'CompanyManagementPayload': { kind: 'OBJECT'; name: 'CompanyManagementPayload'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'CompanyManagement'; ofType: null; } }; }; };
    'CompanyOnboarding': { kind: 'OBJECT'; name: 'CompanyOnboarding'; fields: { 'board': { name: 'board'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'capTable': { name: 'capTable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'committees': { name: 'committees'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'incentives': { name: 'incentives'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'logo': { name: 'logo'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'stamp': { name: 'stamp'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; }; };
    'CompanyParty': { kind: 'OBJECT'; name: 'CompanyParty'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'NaturalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'I18nString'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'I18nString'; ofType: null; } }; }; };
    'CompanyPayload': { kind: 'OBJECT'; name: 'CompanyPayload'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; }; };
    'CompanyPlan': { kind: 'OBJECT'; name: 'CompanyPlan'; fields: { 'addons': { name: 'addons'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanyPlanAddon'; ofType: null; }; } }; 'features': { name: 'features'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'CompanyPlanAddon': { kind: 'OBJECT'; name: 'CompanyPlanAddon'; fields: { 'count': { name: 'count'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'CompanyPlanKey': { name: 'CompanyPlanKey'; enumValues: 'FREE' | 'STARTUP' | 'GROWTH' | 'BASIC' | 'PREMIUM' | 'FROZEN'; };
    'CompanyRegistration': { kind: 'OBJECT'; name: 'CompanyRegistration'; fields: { 'cr': { name: 'cr'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'establishmentDate': { name: 'establishmentDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'BusinessTypeOption'; ofType: null; } }; }; };
    'CompanyRegistrationApprovalTodo': { kind: 'OBJECT'; name: 'CompanyRegistrationApprovalTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'CompanyRegistrationDelegationTodo': { kind: 'OBJECT'; name: 'CompanyRegistrationDelegationTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'CompanyRegistrationForm': { kind: 'OBJECT'; name: 'CompanyRegistrationForm'; fields: { 'acceptTerms': { name: 'acceptTerms'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'crNumber': { name: 'crNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'interestedInEbanaAsRelationManager': { name: 'interestedInEbanaAsRelationManager'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'legalCapacity': { name: 'legalCapacity'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'selfManagement': { name: 'selfManagement'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'thirdPartyRelationManager': { name: 'thirdPartyRelationManager'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'topAuthorityContact': { name: 'topAuthorityContact'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'topAuthorityContact_email': { name: 'topAuthorityContact_email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'topAuthorityContact_id': { name: 'topAuthorityContact_id'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'topAuthorityContact_mobile': { name: 'topAuthorityContact_mobile'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'CompanyRegistrationInput': { kind: 'INPUT_OBJECT'; name: 'CompanyRegistrationInput'; isOneOf: false; inputFields: [{ name: 'crNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'selfManagement'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'thirdPartyRelationManager'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'interestedInEbanaAsRelationManager'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'topAuthorityContact'; type: { kind: 'INPUT_OBJECT'; name: 'CompanyRegistrationTopAuthorityContactInput'; ofType: null; }; defaultValue: null }, { name: 'legalCapacity'; type: { kind: 'ENUM'; name: 'CompanyRegistrationLegalCapacity'; ofType: null; }; defaultValue: null }, { name: 'acceptTerms'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'CompanyRegistrationLegalCapacity': { name: 'CompanyRegistrationLegalCapacity'; enumValues: 'PROFESSIONAL_FIRM' | 'COMPANY_EMPLOYEE' | 'DIRECTOR' | 'MANAGER' | 'PARTNER' | 'OTHER'; };
    'CompanyRegistrationLegalCapacityOption': { kind: 'OBJECT'; name: 'CompanyRegistrationLegalCapacityOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'CompanyRegistrationLegalCapacity'; ofType: null; } }; }; };
    'CompanyRegistrationTopAuthorityContactInput': { kind: 'INPUT_OBJECT'; name: 'CompanyRegistrationTopAuthorityContactInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'mobile'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CompanySubscription': { kind: 'OBJECT'; name: 'CompanySubscription'; fields: { 'addons': { name: 'addons'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'CompanySubscriptionAddonUsage'; ofType: null; }; } }; 'end': { name: 'end'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'plan': { name: 'plan'; type: { kind: 'OBJECT'; name: 'CompanyPlan'; ofType: null; } }; 'price': { name: 'price'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'start': { name: 'start'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'CompanySubscriptionStatusOption'; ofType: null; } }; }; };
    'CompanySubscriptionAddonUsage': { kind: 'OBJECT'; name: 'CompanySubscriptionAddonUsage'; fields: { 'addon': { name: 'addon'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'CompanySubscriptionPayload': { kind: 'OBJECT'; name: 'CompanySubscriptionPayload'; fields: { 'subscription': { name: 'subscription'; type: { kind: 'OBJECT'; name: 'CompanySubscription'; ofType: null; } }; }; };
    'CompanySubscriptionStatus': { name: 'CompanySubscriptionStatus'; enumValues: 'PENDING' | 'PENDING_ADMIN' | 'CANCELED' | 'ACTIVE' | 'UPGRADED' | 'EXPIRED'; };
    'CompanySubscriptionStatusOption': { kind: 'OBJECT'; name: 'CompanySubscriptionStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'CompanySubscriptionStatus'; ofType: null; } }; }; };
    'CompanyTerminateAgencyForm': { kind: 'OBJECT'; name: 'CompanyTerminateAgencyForm'; fields: { 'agentId': { name: 'agentId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'equityId': { name: 'equityId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'CompanyTerminateAgencyInput': { kind: 'INPUT_OBJECT'; name: 'CompanyTerminateAgencyInput'; isOneOf: false; inputFields: [{ name: 'equityId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'agentId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CompanyUpdateDefaultCardForm': { kind: 'OBJECT'; name: 'CompanyUpdateDefaultCardForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'cardId': { name: 'cardId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'CompanyUpdateDefaultCardInput': { kind: 'INPUT_OBJECT'; name: 'CompanyUpdateDefaultCardInput'; isOneOf: false; inputFields: [{ name: 'cardId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'Country': { kind: 'OBJECT'; name: 'Country'; fields: { 'callingCode': { name: 'callingCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'flagUnicode': { name: 'flagUnicode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'CustomErrorMessage': { kind: 'OBJECT'; name: 'CustomErrorMessage'; fields: { 'sellToUnfit': { name: 'sellToUnfit'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'unfitSeller': { name: 'unfitSeller'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'DashboardInsight': { name: 'DashboardInsight'; enumValues: 'COMPANY_PROFILE' | 'BOARD_MEETING' | 'ASSEMBLY_MEETING' | 'COMMITTEE_MEETING' | 'INCENTIVE_PROGRAM' | 'PRESS_RELEASE' | 'GRANT_PRIVILEGES'; };
    'DateFormField': { kind: 'OBJECT'; name: 'DateFormField'; fields: { 'after': { name: 'after'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'before': { name: 'before'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'timeline': { name: 'timeline'; type: { kind: 'ENUM'; name: 'Timeline'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'DateTimeFormField': { kind: 'OBJECT'; name: 'DateTimeFormField'; fields: { 'after': { name: 'after'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'before': { name: 'before'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'timeline': { name: 'timeline'; type: { kind: 'ENUM'; name: 'Timeline'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'DayOfWeek': { name: 'DayOfWeek'; enumValues: 'SUNDAY' | 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY' | 'SATURDAY'; };
    'DayOfWeekOption': { kind: 'OBJECT'; name: 'DayOfWeekOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'DayOfWeek'; ofType: null; } }; }; };
    'DefaultItems': { kind: 'OBJECT'; name: 'DefaultItems'; fields: { 'items': { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'DelegatedCompany': { kind: 'OBJECT'; name: 'DelegatedCompany'; fields: { 'assembly': { name: 'assembly'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionOption'; ofType: null; }; }; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'end': { name: 'end'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'start': { name: 'start'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; }; };
    'DirectGiftCreateForm': { kind: 'OBJECT'; name: 'DirectGiftCreateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'password': { name: 'password'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'paymentCallbackUrl': { name: 'paymentCallbackUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'recipientId': { name: 'recipientId'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'recipientId_type': { name: 'recipientId_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'recipientId_value': { name: 'recipientId_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secret': { name: 'secret'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'DirectGiftCreateInput': { kind: 'INPUT_OBJECT'; name: 'DirectGiftCreateInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'recipientId'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'secret'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'password'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'paymentCallbackUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; }; defaultValue: null }]; };
    'DirectGiftCreatePayload': { kind: 'OBJECT'; name: 'DirectGiftCreatePayload'; fields: { 'contract': { name: 'contract'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTrade'; ofType: null; } }; 'payment': { name: 'payment'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'DirectSellCreateForm': { kind: 'OBJECT'; name: 'DirectSellCreateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'buyerId': { name: 'buyerId'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'buyerId_type': { name: 'buyerId_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'buyerId_value': { name: 'buyerId_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'ComboBoxCompany'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'iban': { name: 'iban'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'password': { name: 'password'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'paymentCallbackUrl': { name: 'paymentCallbackUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'secret': { name: 'secret'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'sharePrice': { name: 'sharePrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'DirectSellCreateInput': { kind: 'INPUT_OBJECT'; name: 'DirectSellCreateInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'buyerId'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'sharePrice'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'iban'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'secret'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'password'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'paymentCallbackUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; }; defaultValue: null }]; };
    'DirectSellCreatePayload': { kind: 'OBJECT'; name: 'DirectSellCreatePayload'; fields: { 'contract': { name: 'contract'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTrade'; ofType: null; } }; 'payment': { name: 'payment'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'DirectTrade': { kind: 'OBJECT'; name: 'DirectTrade'; fields: { 'acceptForm': { name: 'acceptForm'; type: { kind: 'OBJECT'; name: 'DirectTradeAcceptForm'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'contract': { name: 'contract'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'contractPdf': { name: 'contractPdf'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'DirectTradeDeleteForm'; ofType: null; } }; 'grantor': { name: 'grantor'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'iban': { name: 'iban'; type: { kind: 'OBJECT'; name: 'Iban'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'otherParty': { name: 'otherParty'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'paymentAmount': { name: 'paymentAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'recipient': { name: 'recipient'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'rejectForm': { name: 'rejectForm'; type: { kind: 'OBJECT'; name: 'DirectTradeRejectForm'; ofType: null; } }; 'settledAt': { name: 'settledAt'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'shareClassName': { name: 'shareClassName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareClassPrefix': { name: 'shareClassPrefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'sharePrice': { name: 'sharePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'DirectTradeStatusOption'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'DirectTradeTypeOption'; ofType: null; } }; }; };
    'DirectTradeAcceptForm': { kind: 'OBJECT'; name: 'DirectTradeAcceptForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'password': { name: 'password'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'receipt': { name: 'receipt'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'secret': { name: 'secret'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'DirectTradeAcceptInput': { kind: 'INPUT_OBJECT'; name: 'DirectTradeAcceptInput'; isOneOf: false; inputFields: [{ name: 'receipt'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }, { name: 'secret'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'password'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'DirectTradeAcceptPayload': { kind: 'OBJECT'; name: 'DirectTradeAcceptPayload'; fields: { 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTrade'; ofType: null; } }; }; };
    'DirectTradeAcceptTodo': { kind: 'OBJECT'; name: 'DirectTradeAcceptTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'DirectTradeStatusOption'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'DirectTradeCancelForm': { kind: 'OBJECT'; name: 'DirectTradeCancelForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'note': { name: 'note'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'DirectTradeCancelInput': { kind: 'INPUT_OBJECT'; name: 'DirectTradeCancelInput'; isOneOf: false; inputFields: [{ name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'DirectTradeConfirmForm': { kind: 'OBJECT'; name: 'DirectTradeConfirmForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'DirectTradeDeleteForm': { kind: 'OBJECT'; name: 'DirectTradeDeleteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'DirectTradeFilter': { kind: 'INPUT_OBJECT'; name: 'DirectTradeFilter'; isOneOf: false; inputFields: [{ name: 'type'; type: { kind: 'ENUM'; name: 'DirectTradeType'; ofType: null; }; defaultValue: null }, { name: 'type_in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'DirectTradeType'; ofType: null; }; }; defaultValue: null }, { name: 'type_nin'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'DirectTradeType'; ofType: null; }; }; defaultValue: null }, { name: 'status'; type: { kind: 'ENUM'; name: 'DirectTradeStatus'; ofType: null; }; defaultValue: null }, { name: 'status_in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'DirectTradeStatus'; ofType: null; }; }; defaultValue: null }, { name: 'status_nin'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'DirectTradeStatus'; ofType: null; }; }; defaultValue: null }, { name: 'and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'DirectTradeFilter'; ofType: null; }; }; }; defaultValue: null }, { name: 'or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'DirectTradeFilter'; ofType: null; }; }; }; defaultValue: null }, { name: 'not'; type: { kind: 'INPUT_OBJECT'; name: 'DirectTradeFilter'; ofType: null; }; defaultValue: null }]; };
    'DirectTradeManagement': { kind: 'OBJECT'; name: 'DirectTradeManagement'; fields: { 'cancelForm': { name: 'cancelForm'; type: { kind: 'OBJECT'; name: 'DirectTradeCancelForm'; ofType: null; } }; 'confirmForm': { name: 'confirmForm'; type: { kind: 'OBJECT'; name: 'DirectTradeConfirmForm'; ofType: null; } }; 'contract': { name: 'contract'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'contractPdf': { name: 'contractPdf'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'grantor': { name: 'grantor'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'iban': { name: 'iban'; type: { kind: 'OBJECT'; name: 'Iban'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'paymentAmount': { name: 'paymentAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'recipient': { name: 'recipient'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'settledAt': { name: 'settledAt'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'shareClassName': { name: 'shareClassName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareClassPrefix': { name: 'shareClassPrefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'sharePrice': { name: 'sharePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'shortId': { name: 'shortId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'DirectTradeStatusOption'; ofType: null; } }; 'transactionReceipt': { name: 'transactionReceipt'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'DirectTradeTypeOption'; ofType: null; } }; }; };
    'DirectTradeManagementPaginated': { kind: 'OBJECT'; name: 'DirectTradeManagementPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DirectTradeManagement'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'DirectTradeManagementPayload': { kind: 'OBJECT'; name: 'DirectTradeManagementPayload'; fields: { 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTradeManagement'; ofType: null; } }; }; };
    'DirectTradePaginated': { kind: 'OBJECT'; name: 'DirectTradePaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DirectTrade'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'DirectTradePayload': { kind: 'OBJECT'; name: 'DirectTradePayload'; fields: { 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTrade'; ofType: null; } }; }; };
    'DirectTradePaymentTodo': { kind: 'OBJECT'; name: 'DirectTradePaymentTodo'; fields: { 'amount': { name: 'amount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'DirectTradeRejectForm': { kind: 'OBJECT'; name: 'DirectTradeRejectForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'DirectTradeStatus': { name: 'DirectTradeStatus'; enumValues: 'DRAFT' | 'DELETED' | 'INITIATED' | 'ACCEPTED' | 'REJECTED' | 'CONFIRMED' | 'CANCELED' | 'SETTLED'; };
    'DirectTradeStatusOption': { kind: 'OBJECT'; name: 'DirectTradeStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'DirectTradeStatus'; ofType: null; } }; }; };
    'DirectTradeType': { name: 'DirectTradeType'; enumValues: 'SELL' | 'GIFT'; };
    'DirectTradeTypeOption': { kind: 'OBJECT'; name: 'DirectTradeTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'DirectTradeType'; ofType: null; } }; }; };
    'EIG': { kind: 'OBJECT'; name: 'EIG'; fields: { 'acceptOfferForm': { name: 'acceptOfferForm'; type: { kind: 'OBJECT'; name: 'EigOfferAcceptForm'; ofType: null; } }; 'approver': { name: 'approver'; type: { kind: 'OBJECT'; name: 'EIGApprover'; ofType: null; } }; 'award': { name: 'award'; type: { kind: 'UNION'; name: 'Award'; ofType: null; } }; 'awardType': { name: 'awardType'; type: { kind: 'ENUM'; name: 'AwardType'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'companyLogo': { name: 'companyLogo'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'companyName': { name: 'companyName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'contract': { name: 'contract'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'contractText': { name: 'contractText'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'eip': { name: 'eip'; type: { kind: 'OBJECT'; name: 'EIPManagement'; ofType: null; } }; 'eipId': { name: 'eipId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'exercisedSummary': { name: 'exercisedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'fullVestingDate': { name: 'fullVestingDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'grantDate': { name: 'grantDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'grantPrice': { name: 'grantPrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'grantee': { name: 'grantee'; type: { kind: 'OBJECT'; name: 'Grantee'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'nextVestingDate': { name: 'nextVestingDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'nextVestingShareCount': { name: 'nextVestingShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'nonVestedSummary': { name: 'nonVestedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'position': { name: 'position'; type: { kind: 'OBJECT'; name: 'EquityGrantPosition'; ofType: null; } }; 'referBackByGranteeForm': { name: 'referBackByGranteeForm'; type: { kind: 'OBJECT'; name: 'JustificationForm'; ofType: null; } }; 'referral': { name: 'referral'; type: { kind: 'OBJECT'; name: 'Referral'; ofType: null; } }; 'settledSummary': { name: 'settledSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'EIGStatusOption'; ofType: null; } }; 'termination': { name: 'termination'; type: { kind: 'OBJECT'; name: 'Termination'; ofType: null; } }; 'terms': { name: 'terms'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'VestingTerm'; ofType: null; }; } }; 'vestedSummary': { name: 'vestedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vestingStartDate': { name: 'vestingStartDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; }; };
    'EIGApproveExerciseForm': { kind: 'OBJECT'; name: 'EIGApproveExerciseForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'entitledShares': { name: 'entitledShares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'termId': { name: 'termId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EIGApproveExerciseInput': { kind: 'INPUT_OBJECT'; name: 'EIGApproveExerciseInput'; isOneOf: false; inputFields: [{ name: 'termId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'entitledShares'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'EIGApprover': { kind: 'OBJECT'; name: 'EIGApprover'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'NaturalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'EIGAutomaticVestingScheduleInput': { kind: 'INPUT_OBJECT'; name: 'EIGAutomaticVestingScheduleInput'; isOneOf: false; inputFields: [{ name: 'start'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'termCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'length'; type: { kind: 'ENUM'; name: 'TermLength'; ofType: null; }; defaultValue: null }]; };
    'EIGCreateForm': { kind: 'OBJECT'; name: 'EIGCreateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'award': { name: 'award'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'award_phantom': { name: 'award_phantom'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'award_phantom__': { name: 'award_phantom__'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'award_sars': { name: 'award_sars'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'award_sars_currentPrice': { name: 'award_sars_currentPrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'award_stockOptions': { name: 'award_stockOptions'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'award_stockOptions_exercisePrice': { name: 'award_stockOptions_exercisePrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'eipId': { name: 'eipId'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'fsv': { name: 'fsv'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'granteeDepartment': { name: 'granteeDepartment'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'granteeId': { name: 'granteeId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'granteeName': { name: 'granteeName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'granteeNationality': { name: 'granteeNationality'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'vestingSchedule': { name: 'vestingSchedule'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'vestingSchedule_automatic': { name: 'vestingSchedule_automatic'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'vestingSchedule_automatic_length': { name: 'vestingSchedule_automatic_length'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'vestingSchedule_automatic_start': { name: 'vestingSchedule_automatic_start'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'vestingSchedule_automatic_termCount': { name: 'vestingSchedule_automatic_termCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'vestingSchedule_custom': { name: 'vestingSchedule_custom'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'vestingSchedule_custom_start': { name: 'vestingSchedule_custom_start'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'vestingSchedule_custom_terms': { name: 'vestingSchedule_custom_terms'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'vestingSchedule_custom_terms_due': { name: 'vestingSchedule_custom_terms_due'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'vestingSchedule_custom_terms_expiryDate': { name: 'vestingSchedule_custom_terms_expiryDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'vestingSchedule_custom_terms_kpi': { name: 'vestingSchedule_custom_terms_kpi'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'vestingSchedule_custom_terms_shareCount': { name: 'vestingSchedule_custom_terms_shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; }; };
    'EIGCreateInput': { kind: 'INPUT_OBJECT'; name: 'EIGCreateInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'eipId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'award'; type: { kind: 'INPUT_OBJECT'; name: 'AwardInput'; ofType: null; }; defaultValue: null }, { name: 'granteeName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'granteeId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'granteeNationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'granteeDepartment'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'vestingSchedule'; type: { kind: 'INPUT_OBJECT'; name: 'EIGVestingScheduleInput'; ofType: null; }; defaultValue: null }, { name: 'fsv'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'EIGCustomVestingScheduleInput': { kind: 'INPUT_OBJECT'; name: 'EIGCustomVestingScheduleInput'; isOneOf: false; inputFields: [{ name: 'start'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'terms'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'EIGVestingTermInput'; ofType: null; }; }; }; defaultValue: null }]; };
    'EIGExerciseForm': { kind: 'OBJECT'; name: 'EIGExerciseForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'termId': { name: 'termId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EIGExerciseInput': { kind: 'INPUT_OBJECT'; name: 'EIGExerciseInput'; isOneOf: false; inputFields: [{ name: 'termId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'EIGFilter': { kind: 'INPUT_OBJECT'; name: 'EIGFilter'; isOneOf: false; inputFields: [{ name: 'status'; type: { kind: 'ENUM'; name: 'EIGStatus'; ofType: null; }; defaultValue: null }, { name: 'status_in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'EIGStatus'; ofType: null; }; }; defaultValue: null }, { name: 'status_nin'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'EIGStatus'; ofType: null; }; }; defaultValue: null }, { name: 'awardType'; type: { kind: 'ENUM'; name: 'AwardType'; ofType: null; }; defaultValue: null }, { name: 'awardType_in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'AwardType'; ofType: null; }; }; defaultValue: null }, { name: 'awardType_nin'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'AwardType'; ofType: null; }; }; defaultValue: null }, { name: 'eipId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'eipId_in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'eipId_nin'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'eipId_contains'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'EIGFilter'; ofType: null; }; }; }; defaultValue: null }, { name: 'or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'EIGFilter'; ofType: null; }; }; }; defaultValue: null }, { name: 'not'; type: { kind: 'INPUT_OBJECT'; name: 'EIGFilter'; ofType: null; }; defaultValue: null }]; };
    'EIGManagement': { kind: 'OBJECT'; name: 'EIGManagement'; fields: { 'approver': { name: 'approver'; type: { kind: 'OBJECT'; name: 'EIGApprover'; ofType: null; } }; 'award': { name: 'award'; type: { kind: 'UNION'; name: 'Award'; ofType: null; } }; 'awardType': { name: 'awardType'; type: { kind: 'ENUM'; name: 'AwardType'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'contract': { name: 'contract'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'contractText': { name: 'contractText'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'EigDeleteForm'; ofType: null; } }; 'eip': { name: 'eip'; type: { kind: 'OBJECT'; name: 'EIPManagement'; ofType: null; } }; 'eipId': { name: 'eipId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'exercisedSummary': { name: 'exercisedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'grantPrice': { name: 'grantPrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'grantee': { name: 'grantee'; type: { kind: 'OBJECT'; name: 'Grantee'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'nonVestedSummary': { name: 'nonVestedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'referBackByApproverForm': { name: 'referBackByApproverForm'; type: { kind: 'OBJECT'; name: 'JustificationForm'; ofType: null; } }; 'referral': { name: 'referral'; type: { kind: 'OBJECT'; name: 'Referral'; ofType: null; } }; 'sendOfferForm': { name: 'sendOfferForm'; type: { kind: 'OBJECT'; name: 'EigOfferSendForm'; ofType: null; } }; 'settledSummary': { name: 'settledSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'EIGStatusOption'; ofType: null; } }; 'submitForm': { name: 'submitForm'; type: { kind: 'OBJECT'; name: 'EigSubmitForm'; ofType: null; } }; 'terminateForm': { name: 'terminateForm'; type: { kind: 'OBJECT'; name: 'JustificationForm'; ofType: null; } }; 'termination': { name: 'termination'; type: { kind: 'OBJECT'; name: 'Termination'; ofType: null; } }; 'terms': { name: 'terms'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'VestingTermManagement'; ofType: null; }; } }; 'updateForm': { name: 'updateForm'; type: { kind: 'OBJECT'; name: 'EIGUpdateForm'; ofType: null; } }; 'updateForm_granteeNationality': { name: 'updateForm_granteeNationality'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; } }; 'vestedSummary': { name: 'vestedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'EIGManagementPaginated': { kind: 'OBJECT'; name: 'EIGManagementPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'EIGManagement'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'EIGManagementPayload': { kind: 'OBJECT'; name: 'EIGManagementPayload'; fields: { 'eig': { name: 'eig'; type: { kind: 'OBJECT'; name: 'EIGManagement'; ofType: null; } }; }; };
    'EIGPaginated': { kind: 'OBJECT'; name: 'EIGPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'EIG'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'EIGPayload': { kind: 'OBJECT'; name: 'EIGPayload'; fields: { 'eig': { name: 'eig'; type: { kind: 'OBJECT'; name: 'EIG'; ofType: null; } }; }; };
    'EIGRejectExerciseForm': { kind: 'OBJECT'; name: 'EIGRejectExerciseForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'reason': { name: 'reason'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'termId': { name: 'termId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EIGRejectExerciseInput': { kind: 'INPUT_OBJECT'; name: 'EIGRejectExerciseInput'; isOneOf: false; inputFields: [{ name: 'termId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'EIGSettleForm': { kind: 'OBJECT'; name: 'EIGSettleForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'fsv': { name: 'fsv'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'termId': { name: 'termId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EIGSettleInput': { kind: 'INPUT_OBJECT'; name: 'EIGSettleInput'; isOneOf: false; inputFields: [{ name: 'termId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'fsv'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'EIGSort': { kind: 'INPUT_OBJECT'; name: 'EIGSort'; isOneOf: false; inputFields: [{ name: 'grantDate'; type: { kind: 'ENUM'; name: 'SortDirection'; ofType: null; }; defaultValue: null }]; };
    'EIGStatus': { name: 'EIGStatus'; enumValues: 'DRAFT' | 'UNDER_REVIEW' | 'PENDING_GRANTEE_RESPONSE' | 'GRANTED' | 'FINISHED' | 'TERMINATED'; };
    'EIGStatusOption': { kind: 'OBJECT'; name: 'EIGStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'EIGStatus'; ofType: null; } }; }; };
    'EIGStatuses': { name: 'EIGStatuses'; enumValues: 'DRAFT' | 'UNDER_REVIEW' | 'PENDING_GRANTEE_RESPONSE' | 'GRANTED' | 'FINISHED' | 'TERMINATED'; };
    'EIGStatusesOption': { kind: 'OBJECT'; name: 'EIGStatusesOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'EIGStatuses'; ofType: null; } }; }; };
    'EIGUpdateForm': { kind: 'OBJECT'; name: 'EIGUpdateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'fsv': { name: 'fsv'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'granteeDepartment': { name: 'granteeDepartment'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'granteeId': { name: 'granteeId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'granteeName': { name: 'granteeName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'granteeNationality': { name: 'granteeNationality'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EIGUpdateInput': { kind: 'INPUT_OBJECT'; name: 'EIGUpdateInput'; isOneOf: false; inputFields: [{ name: 'granteeName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'granteeId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'granteeDepartment'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'granteeNationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'fsv'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'EIGVestingScheduleInput': { kind: 'INPUT_OBJECT'; name: 'EIGVestingScheduleInput'; isOneOf: false; inputFields: [{ name: 'automatic'; type: { kind: 'INPUT_OBJECT'; name: 'EIGAutomaticVestingScheduleInput'; ofType: null; }; defaultValue: null }, { name: 'custom'; type: { kind: 'INPUT_OBJECT'; name: 'EIGCustomVestingScheduleInput'; ofType: null; }; defaultValue: null }]; };
    'EIGVestingTermInput': { kind: 'INPUT_OBJECT'; name: 'EIGVestingTermInput'; isOneOf: false; inputFields: [{ name: 'due'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'expiryDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'kpi'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'EIP': { kind: 'OBJECT'; name: 'EIP'; fields: { 'assemblyApprovalDate': { name: 'assemblyApprovalDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'boardApprovalDate': { name: 'boardApprovalDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'contract': { name: 'contract'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'contractText': { name: 'contractText'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'EIPCreateForm': { kind: 'OBJECT'; name: 'EIPCreateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'assemblyApprovalDate': { name: 'assemblyApprovalDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardApprovalDate': { name: 'boardApprovalDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'shareClassPrefix': { name: 'shareClassPrefix'; type: { kind: 'OBJECT'; name: 'ComboBoxShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EIPCreateInput': { kind: 'INPUT_OBJECT'; name: 'EIPCreateInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'boardApprovalDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'assemblyApprovalDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'shareClassPrefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'EIPManagement': { kind: 'OBJECT'; name: 'EIPManagement'; fields: { 'assemblyApprovalDate': { name: 'assemblyApprovalDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'boardApprovalDate': { name: 'boardApprovalDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'contract': { name: 'contract'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'contractText': { name: 'contractText'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'executiveSummary': { name: 'executiveSummary'; type: { kind: 'OBJECT'; name: 'EquityIncentiveExecutiveSummary'; ofType: null; } }; 'grantsSummary': { name: 'grantsSummary'; type: { kind: 'OBJECT'; name: 'GrantsSummary'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'issuedGrantsShareCount': { name: 'issuedGrantsShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'remainingPoolShares': { name: 'remainingPoolShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'reporting': { name: 'reporting'; type: { kind: 'OBJECT'; name: 'EquityIncentiveReporting'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'shareDistributionSummary': { name: 'shareDistributionSummary'; type: { kind: 'OBJECT'; name: 'ShareDistributionSummary'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'EipStatusOption'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'updateForm': { name: 'updateForm'; type: { kind: 'OBJECT'; name: 'EIPUpdateForm'; ofType: null; } }; }; };
    'EIPManagementPaginated': { kind: 'OBJECT'; name: 'EIPManagementPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'EIPManagement'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'EIPManagementPayload': { kind: 'OBJECT'; name: 'EIPManagementPayload'; fields: { 'eip': { name: 'eip'; type: { kind: 'OBJECT'; name: 'EIPManagement'; ofType: null; } }; }; };
    'EIPUpdateForm': { kind: 'OBJECT'; name: 'EIPUpdateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'assemblyApprovalDate': { name: 'assemblyApprovalDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardApprovalDate': { name: 'boardApprovalDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'shareClassPrefix': { name: 'shareClassPrefix'; type: { kind: 'OBJECT'; name: 'ComboBoxShareClass'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EIPUpdateInput': { kind: 'INPUT_OBJECT'; name: 'EIPUpdateInput'; isOneOf: false; inputFields: [{ name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'boardApprovalDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'assemblyApprovalDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'shareClassPrefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'EbanaRoomMeetingInput': { kind: 'INPUT_OBJECT'; name: 'EbanaRoomMeetingInput'; isOneOf: false; inputFields: [{ name: 'ignored'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'EdaaPortfolio': { kind: 'OBJECT'; name: 'EdaaPortfolio'; fields: { 'broker': { name: 'broker'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'number': { name: 'number'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'EigDeleteForm': { kind: 'OBJECT'; name: 'EigDeleteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EigGranteeApprovalTodo': { kind: 'OBJECT'; name: 'EigGranteeApprovalTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'EIGStatusesOption'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'EigOfferAcceptForm': { kind: 'OBJECT'; name: 'EigOfferAcceptForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EigOfferSendForm': { kind: 'OBJECT'; name: 'EigOfferSendForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'fsv': { name: 'fsv'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EigOfferSendInput': { kind: 'INPUT_OBJECT'; name: 'EigOfferSendInput'; isOneOf: false; inputFields: [{ name: 'fsv'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'EigSendOfferTodo': { kind: 'OBJECT'; name: 'EigSendOfferTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'EigSettleTermTodo': { kind: 'OBJECT'; name: 'EigSettleTermTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'termId': { name: 'termId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'EigSubmitForm': { kind: 'OBJECT'; name: 'EigSubmitForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'EipStatus': { name: 'EipStatus'; enumValues: 'ACTIVE' | 'INACTIVE'; };
    'EipStatusOption': { kind: 'OBJECT'; name: 'EipStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'EipStatus'; ofType: null; } }; }; };
    'Equity': { kind: 'INTERFACE'; name: 'Equity'; fields: { 'agent': { name: 'agent'; type: { kind: 'OBJECT'; name: 'EquityAgent'; ofType: null; } }; 'averageCostPerShares': { name: 'averageCostPerShares'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'certificate': { name: 'certificate'; type: { kind: 'OBJECT'; name: 'EquityCertificate'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'marketValue': { name: 'marketValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'policyAccepted': { name: 'policyAccepted'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'position': { name: 'position'; type: { kind: 'OBJECT'; name: 'EquityPosition'; ofType: null; } }; 'positions': { name: 'positions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityPosition'; ofType: null; }; } }; 'profitAndLoss': { name: 'profitAndLoss'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'profitAndLossPercent': { name: 'profitAndLossPercent'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'recentRecipients': { name: 'recentRecipients'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; }; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'totalCost': { name: 'totalCost'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalOwnership': { name: 'totalOwnership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'totalShares': { name: 'totalShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'tradingPolicy': { name: 'tradingPolicy'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; }; possibleTypes: 'AsEquity' | 'CompanyEquity' | 'MeEquity'; };
    'EquityAgent': { kind: 'OBJECT'; name: 'EquityAgent'; fields: { 'agencyNumber': { name: 'agencyNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'dateOfIssuance': { name: 'dateOfIssuance'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'expirationDate': { name: 'expirationDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'EquityCertificate': { kind: 'OBJECT'; name: 'EquityCertificate'; fields: { 'issueDate': { name: 'issueDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'number': { name: 'number'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'pdf': { name: 'pdf'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; }; };
    'EquityGrantPosition': { kind: 'OBJECT'; name: 'EquityGrantPosition'; fields: { 'change': { name: 'change'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'sharePrice': { name: 'sharePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalAmount': { name: 'totalAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; }; };
    'EquityIncentiveAwardReports': { kind: 'OBJECT'; name: 'EquityIncentiveAwardReports'; fields: { 'grantedShares': { name: 'grantedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'grantsCount': { name: 'grantsCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; }; };
    'EquityIncentiveCompanyReports': { kind: 'OBJECT'; name: 'EquityIncentiveCompanyReports'; fields: { 'activeGrants': { name: 'activeGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'availableShares': { name: 'availableShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'awards': { name: 'awards'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveAwardReports'; ofType: null; }; } }; 'exercisedShares': { name: 'exercisedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'exercisingShares': { name: 'exercisingShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'financialReportXlsx': { name: 'financialReportXlsx'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'finishedGrants': { name: 'finishedGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'forfeitedGrants': { name: 'forfeitedGrants'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveGrantReports'; ofType: null; }; } }; 'forfeitedShares': { name: 'forfeitedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'grantedShares': { name: 'grantedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'granteeCount': { name: 'granteeCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'grants': { name: 'grants'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveGrantReports'; ofType: null; }; } }; 'grantsCount': { name: 'grantsCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'optionsExercises': { name: 'optionsExercises'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveVestingTermReports'; ofType: null; }; } }; 'pendingGrants': { name: 'pendingGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'programs': { name: 'programs'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveProgramReports'; ofType: null; }; } }; 'settledShares': { name: 'settledShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'stockOptionGainSummary': { name: 'stockOptionGainSummary'; type: { kind: 'OBJECT'; name: 'SummaryStatistics'; ofType: null; } }; 'stockOptionsGainChart': { name: 'stockOptionsGainChart'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StockOptionsGainChartEntry'; ofType: null; }; } }; 'terminatedGrants': { name: 'terminatedGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'totalShareCount': { name: 'totalShareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'vestedShares': { name: 'vestedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vestingShares': { name: 'vestingShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'EquityIncentiveCompensationSummaryEntry': { kind: 'OBJECT'; name: 'EquityIncentiveCompensationSummaryEntry'; fields: { 'end': { name: 'end'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'exercisePrice': { name: 'exercisePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'grantedShares': { name: 'grantedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'grantee': { name: 'grantee'; type: { kind: 'OBJECT'; name: 'Grantee'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'start': { name: 'start'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'termsCount': { name: 'termsCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'EquityIncentiveExecutiveSummary': { kind: 'OBJECT'; name: 'EquityIncentiveExecutiveSummary'; fields: { 'forfeited': { name: 'forfeited'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'granted': { name: 'granted'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'stockOptionGainSummary': { name: 'stockOptionGainSummary'; type: { kind: 'OBJECT'; name: 'SummaryStatistics'; ofType: null; } }; 'stockOptionsGainChart': { name: 'stockOptionsGainChart'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StockOptionsGainChartEntry'; ofType: null; }; } }; 'typeOfEquity': { name: 'typeOfEquity'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'TypeOfEquity'; ofType: null; }; } }; }; };
    'EquityIncentiveGrantReports': { kind: 'OBJECT'; name: 'EquityIncentiveGrantReports'; fields: { 'award': { name: 'award'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; 'department': { name: 'department'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'exercisePrice': { name: 'exercisePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'exercisedShares': { name: 'exercisedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'exercisingShares': { name: 'exercisingShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'firstTermName': { name: 'firstTermName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'forfeitedShares': { name: 'forfeitedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'grantDate': { name: 'grantDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'grantedShares': { name: 'grantedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'grantee': { name: 'grantee'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'lastTermName': { name: 'lastTermName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'settledShares': { name: 'settledShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'terms': { name: 'terms'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveVestingTermReports'; ofType: null; }; } }; 'termsCount': { name: 'termsCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'usedShares': { name: 'usedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vestedShares': { name: 'vestedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vestingEnd': { name: 'vestingEnd'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'vestingPeriod': { name: 'vestingPeriod'; type: { kind: 'OBJECT'; name: 'Period'; ofType: null; } }; 'vestingShares': { name: 'vestingShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vestingStart': { name: 'vestingStart'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; }; };
    'EquityIncentiveProgramReports': { kind: 'OBJECT'; name: 'EquityIncentiveProgramReports'; fields: { 'activeGrants': { name: 'activeGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'availableShares': { name: 'availableShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'awards': { name: 'awards'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveAwardReports'; ofType: null; }; } }; 'exercisedShares': { name: 'exercisedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'exercisingShares': { name: 'exercisingShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'financialReportXlsx': { name: 'financialReportXlsx'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'finishedGrants': { name: 'finishedGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'forfeitedGrants': { name: 'forfeitedGrants'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveGrantReports'; ofType: null; }; } }; 'forfeitedShares': { name: 'forfeitedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'grantedShares': { name: 'grantedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'granteeCount': { name: 'granteeCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'grants': { name: 'grants'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveGrantReports'; ofType: null; }; } }; 'grantsCount': { name: 'grantsCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'optionsExercises': { name: 'optionsExercises'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveVestingTermReports'; ofType: null; }; } }; 'pendingGrants': { name: 'pendingGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'settledShares': { name: 'settledShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'stockOptionGainSummary': { name: 'stockOptionGainSummary'; type: { kind: 'OBJECT'; name: 'SummaryStatistics'; ofType: null; } }; 'stockOptionsGainChart': { name: 'stockOptionsGainChart'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StockOptionsGainChartEntry'; ofType: null; }; } }; 'terminatedGrants': { name: 'terminatedGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vestedShares': { name: 'vestedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vestingShares': { name: 'vestingShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'EquityIncentiveReporting': { kind: 'OBJECT'; name: 'EquityIncentiveReporting'; fields: { 'compensationSummary': { name: 'compensationSummary'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveCompensationSummaryEntry'; ofType: null; }; } }; }; };
    'EquityIncentiveReports': { kind: 'INTERFACE'; name: 'EquityIncentiveReports'; fields: { 'activeGrants': { name: 'activeGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'availableShares': { name: 'availableShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'awards': { name: 'awards'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveAwardReports'; ofType: null; }; } }; 'financialReportXlsx': { name: 'financialReportXlsx'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'finishedGrants': { name: 'finishedGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'forfeitedGrants': { name: 'forfeitedGrants'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveGrantReports'; ofType: null; }; } }; 'forfeitedShares': { name: 'forfeitedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'grantedShares': { name: 'grantedShares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'granteeCount': { name: 'granteeCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'grants': { name: 'grants'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveGrantReports'; ofType: null; }; } }; 'grantsCount': { name: 'grantsCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'optionsExercises': { name: 'optionsExercises'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityIncentiveVestingTermReports'; ofType: null; }; } }; 'pendingGrants': { name: 'pendingGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'stockOptionGainSummary': { name: 'stockOptionGainSummary'; type: { kind: 'OBJECT'; name: 'SummaryStatistics'; ofType: null; } }; 'stockOptionsGainChart': { name: 'stockOptionsGainChart'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StockOptionsGainChartEntry'; ofType: null; }; } }; 'terminatedGrants': { name: 'terminatedGrants'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; possibleTypes: 'EquityIncentiveCompanyReports' | 'EquityIncentiveProgramReports'; };
    'EquityIncentiveVestingTermReports': { kind: 'OBJECT'; name: 'EquityIncentiveVestingTermReports'; fields: { 'closingPriceAtExerciseDate': { name: 'closingPriceAtExerciseDate'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'closingPriceAtGrantDate': { name: 'closingPriceAtGrantDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'exerciseDate': { name: 'exerciseDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'exercisePrice': { name: 'exercisePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'exerciseRequestDate': { name: 'exerciseRequestDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'grantDate': { name: 'grantDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'grantee': { name: 'grantee'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'optionsGain': { name: 'optionsGain'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'settlementDate': { name: 'settlementDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'shares': { name: 'shares'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vestingEnd': { name: 'vestingEnd'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'vestingStart': { name: 'vestingStart'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; }; };
    'EquityPosition': { kind: 'OBJECT'; name: 'EquityPosition'; fields: { 'averageCostPerShares': { name: 'averageCostPerShares'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'directGiftCreateForm': { name: 'directGiftCreateForm'; type: { kind: 'OBJECT'; name: 'DirectGiftCreateForm'; ofType: null; } }; 'directSellCreateForm': { name: 'directSellCreateForm'; type: { kind: 'OBJECT'; name: 'DirectSellCreateForm'; ofType: null; } }; 'marketValue': { name: 'marketValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'profitAndLoss': { name: 'profitAndLoss'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'profitAndLossPercent': { name: 'profitAndLossPercent'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'sharePrice': { name: 'sharePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalCost': { name: 'totalCost'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalOwnership': { name: 'totalOwnership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; }; };
    'EquityPositionRegister': { kind: 'OBJECT'; name: 'EquityPositionRegister'; fields: { 'estimatedMarketCap': { name: 'estimatedMarketCap'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'marketValuation': { name: 'marketValuation'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'ticker': { name: 'ticker'; type: { kind: 'OBJECT'; name: 'StockTicker'; ofType: null; } }; 'top5': { name: 'top5'; type: { kind: 'OBJECT'; name: 'EquityPositionRegisterTop5'; ofType: null; } }; 'totalShareholders': { name: 'totalShareholders'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'EquityPositionRegisterTop5': { kind: 'OBJECT'; name: 'EquityPositionRegisterTop5'; fields: { 'ownership': { name: 'ownership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'shareholders': { name: 'shareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityPositionShareholder'; ofType: null; }; } }; }; };
    'EquityPositionShareholder': { kind: 'OBJECT'; name: 'EquityPositionShareholder'; fields: { 'founder': { name: 'founder'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'totalOwnership': { name: 'totalOwnership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'totalShares': { name: 'totalShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'ExternalMeetingInput': { kind: 'INPUT_OBJECT'; name: 'ExternalMeetingInput'; isOneOf: false; inputFields: [{ name: 'url'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'password'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'ExternalOnlineMeeting': { kind: 'OBJECT'; name: 'ExternalOnlineMeeting'; fields: { 'password': { name: 'password'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'url': { name: 'url'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; }; };
    'FakeFormPayload': { kind: 'OBJECT'; name: 'FakeFormPayload'; fields: { 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'ignored': { name: 'ignored'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; }; };
    'FakeMutationForm': { kind: 'OBJECT'; name: 'FakeMutationForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'big': { name: 'big'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'comboboxCreatable': { name: 'comboboxCreatable'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'comboboxCreatableMultiSelect': { name: 'comboboxCreatableMultiSelect'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'comboboxFreetext': { name: 'comboboxFreetext'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'comboboxLimited': { name: 'comboboxLimited'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'comboboxLimitedMultiSelect': { name: 'comboboxLimitedMultiSelect'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'comboboxObject': { name: 'comboboxObject'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'comboboxObjectCreatable': { name: 'comboboxObjectCreatable'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'confirmPassword': { name: 'confirmPassword'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'country': { name: 'country'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'dateTime': { name: 'dateTime'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'domesticIdentifier': { name: 'domesticIdentifier'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'firstName': { name: 'firstName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'infiniteNumbers': { name: 'infiniteNumbers'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'items': { name: 'items'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'items_number': { name: 'items_number'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'items_text': { name: 'items_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'lastName': { name: 'lastName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'logo': { name: 'logo'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'number': { name: 'number'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'password': { name: 'password'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'select': { name: 'select'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'toggle': { name: 'toggle'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'top': { name: 'top'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'top_number': { name: 'top_number'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'top_text': { name: 'top_text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'FakeMutationInput': { kind: 'INPUT_OBJECT'; name: 'FakeMutationInput'; isOneOf: false; inputFields: [{ name: 'firstName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'password'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'confirmPassword'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'domesticIdentifier'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'number'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'big'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'date'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'dateTime'; type: { kind: 'SCALAR'; name: 'LocalDateTime'; ofType: null; }; defaultValue: null }, { name: 'select'; type: { kind: 'ENUM'; name: 'FakeMutationSelect'; ofType: null; }; defaultValue: null }, { name: 'multiselect'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'FakeMutationSelect'; ofType: null; }; }; defaultValue: null }, { name: 'toggle'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'country'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'infiniteNumbers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'comboboxLimited'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'comboboxCreatable'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'comboboxFreetext'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'comboboxLimitedMultiSelect'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'comboboxCreatableMultiSelect'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'comboboxObject'; type: { kind: 'INPUT_OBJECT'; name: 'I18nStringInput'; ofType: null; }; defaultValue: null }, { name: 'comboboxObjectCreatable'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'logo'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }, { name: 'top'; type: { kind: 'INPUT_OBJECT'; name: 'FakeMutationItemInput'; ofType: null; }; defaultValue: null }, { name: 'items'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FakeMutationItemInput'; ofType: null; }; }; }; defaultValue: null }]; };
    'FakeMutationItemInput': { kind: 'INPUT_OBJECT'; name: 'FakeMutationItemInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'number'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'FakeMutationNumbers': { kind: 'OBJECT'; name: 'FakeMutationNumbers'; fields: { 'n': { name: 'n'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; }; };
    'FakeMutationNumbersConnection': { kind: 'OBJECT'; name: 'FakeMutationNumbersConnection'; fields: { 'edges': { name: 'edges'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'FakeMutationNumbersEdge'; ofType: null; }; }; } }; 'pageInfo': { name: 'pageInfo'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PageInfo'; ofType: null; }; } }; }; };
    'FakeMutationNumbersEdge': { kind: 'OBJECT'; name: 'FakeMutationNumbersEdge'; fields: { 'cursor': { name: 'cursor'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'node': { name: 'node'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FakeMutationNumbers'; ofType: null; }; } }; }; };
    'FakeMutationSelect': { name: 'FakeMutationSelect'; enumValues: 'option1' | 'option2' | 'option3'; };
    'FakeMutationSelectOption': { kind: 'OBJECT'; name: 'FakeMutationSelectOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'FakeMutationSelect'; ofType: null; } }; }; };
    'FeatureFlag': { kind: 'OBJECT'; name: 'FeatureFlag'; fields: { 'key': { name: 'key'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'payload': { name: 'payload'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'FieldStatus': { name: 'FieldStatus'; enumValues: 'REQUIRED' | 'OPTIONAL' | 'UNAVAILABLE' | 'DISABLED'; };
    'File': { kind: 'OBJECT'; name: 'File'; fields: { 'base64': { name: 'base64'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'filename': { name: 'filename'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'url': { name: 'url'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; }; };
    'FileFormField': { kind: 'OBJECT'; name: 'FileFormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiple': { name: 'multiple'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'FileRef': { kind: 'INTERFACE'; name: 'FileRef'; fields: { 'attachmentUrl': { name: 'attachmentUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'inlineUrl': { name: 'inlineUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'url': { name: 'url'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; }; possibleTypes: 'AssemblyFileRef' | 'SimpleFileRef'; };
    'FilterSpecOp': { name: 'FilterSpecOp'; enumValues: 'EQ' | 'NE' | 'IN' | 'NIN' | 'GT' | 'LT' | 'GTE' | 'LTE' | 'CONTAINS'; };
    'FitUnfit': { kind: 'OBJECT'; name: 'FitUnfit'; fields: { 'fit': { name: 'fit'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'policyViewed': { name: 'policyViewed'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; }; };
    'Float': unknown;
    'Form': { kind: 'INTERFACE'; name: 'Form'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; possibleTypes: 'AddonPurchaseForm' | 'AssemblyAmendForm' | 'AssemblyAttendForm' | 'AssemblyCancelForm' | 'AssemblyCloseVotingForm' | 'AssemblyCreateForm' | 'AssemblyDeleteFileForm' | 'AssemblyDeleteForm' | 'AssemblyFinishForm' | 'AssemblyOpenVotingForm' | 'AssemblyPostponeForm' | 'AssemblyPublishForm' | 'AssemblySubmitVoteForm' | 'AssemblyUpdateForm' | 'AssemblyUpdateMinutesForm' | 'AssemblyUploadFileForm' | 'AssignPermissionForm' | 'AutomaticTermForm' | 'BillingInformationUpdateForm' | 'BoardMeetingDraftForm' | 'BoardResolutionDraftForm' | 'BoardSetupForm' | 'CommercialRegistrationLookupForm' | 'CommitteeCircularResolutionDraftForm' | 'CommitteeMeetingDraftForm' | 'CommitteeSetupForm' | 'CompanyAuthorizeAgentForm' | 'CompanyDelegationCreateForm' | 'CompanyDelegationRevokeForm' | 'CompanyDelegationUpdateForm' | 'CompanyDismissInsightForm' | 'CompanyRegistrationForm' | 'CompanyTerminateAgencyForm' | 'CompanyUpdateDefaultCardForm' | 'DirectGiftCreateForm' | 'DirectSellCreateForm' | 'DirectTradeAcceptForm' | 'DirectTradeCancelForm' | 'DirectTradeConfirmForm' | 'DirectTradeDeleteForm' | 'DirectTradeRejectForm' | 'EIGApproveExerciseForm' | 'EIGCreateForm' | 'EIGExerciseForm' | 'EIGRejectExerciseForm' | 'EIGSettleForm' | 'EIGUpdateForm' | 'EIPCreateForm' | 'EIPUpdateForm' | 'EigDeleteForm' | 'EigOfferAcceptForm' | 'EigOfferSendForm' | 'EigSubmitForm' | 'FakeMutationForm' | 'GovernanceCircularResolutionApproveForm' | 'GovernanceCircularResolutionCirculateForm' | 'GovernanceCircularResolutionDeleteForm' | 'GovernanceCircularResolutionVoteForm' | 'GovernanceMeetingAnswerQuestionForm' | 'GovernanceMeetingApproveResultForm' | 'GovernanceMeetingAskQuestionForm' | 'GovernanceMeetingAssignTaskForm' | 'GovernanceMeetingCancelForm' | 'GovernanceMeetingChangeTaskStatusForm' | 'GovernanceMeetingCloseVotingForm' | 'GovernanceMeetingDeleteAnswerForm' | 'GovernanceMeetingDeleteCommentForm' | 'GovernanceMeetingDeleteForm' | 'GovernanceMeetingDeleteQuestionForm' | 'GovernanceMeetingDeleteTaskForm' | 'GovernanceMeetingMinutesUpdateForm' | 'GovernanceMeetingOpenVotingForm' | 'GovernanceMeetingPostCommentForm' | 'GovernanceMeetingPublishForm' | 'GovernanceMeetingRSVPForm' | 'GovernanceMeetingUpdateTaskForm' | 'GovernanceMeetingUploadFileForm' | 'GovernanceMeetingVoteForm' | 'InvitePermissionForm' | 'JustificationForm' | 'MoyasarTokenizeCardForm' | 'PaymentCardDeleteForm' | 'PersonalInformationUpdateForm' | 'PressReleaseCreateForm' | 'RegisterAddCommonForm' | 'RegisterAddPreferredForm' | 'RegisterAddRedeemableForm' | 'RegisterBatchMoveStockForm' | 'RegisterChangeParValueForm' | 'RegisterIssueBonusSharesForm' | 'RegisterIssueSharesForm' | 'RegisterLockTradingInput' | 'RegisterMoveStockForm' | 'RegisterRemoveShareClassForm' | 'RegisterUnlockTradingInput' | 'RegisterUpdateShareholderForm' | 'RevokePermissionForm' | 'SafeCreateForm' | 'SetupCapTableForm' | 'SimpleForm' | 'SubscriptionCreateForm' | 'SubscriptionRenewForm' | 'SubscriptionUpgradeForm' | 'UpdateCompanyForm' | 'UpdateShareholdersFitStatusForm'; };
    'FormConfirmation': { kind: 'OBJECT'; name: 'FormConfirmation'; fields: { 'body': { name: 'body'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'bodyCodes': { name: 'bodyCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'danger': { name: 'danger'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'titleCodes': { name: 'titleCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'FormField': { kind: 'INTERFACE'; name: 'FormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; possibleTypes: 'BasicComboBoxFormField' | 'BooleanFormField' | 'ComboBoxCompany' | 'ComboBoxCompanyPlan' | 'ComboBoxCountry' | 'ComboBoxShareClass' | 'ComboBoxStringOption' | 'DateFormField' | 'DateTimeFormField' | 'FileFormField' | 'ListFormField' | 'NumberFormField' | 'SelectFormField' | 'TextFormField'; };
    'FormFieldGroup': { kind: 'OBJECT'; name: 'FormFieldGroup'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'FormSubmission': { kind: 'OBJECT'; name: 'FormSubmission'; fields: { 'action': { name: 'action'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'actionCodes': { name: 'actionCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'success': { name: 'success'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'successCodes': { name: 'successCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'FullyDilutedBreakdown': { kind: 'OBJECT'; name: 'FullyDilutedBreakdown'; fields: { 'amount': { name: 'amount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'FullyDilutedRegister': { kind: 'OBJECT'; name: 'FullyDilutedRegister'; fields: { 'breakdown': { name: 'breakdown'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'FullyDilutedBreakdown'; ofType: null; }; } }; 'capital': { name: 'capital'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterCapital'; ofType: null; } }; 'newShareholders': { name: 'newShareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholder'; ofType: null; }; } }; 'shareholdersPaginated': { name: 'shareholdersPaginated'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholderPaginated'; ofType: null; } }; 'top5': { name: 'top5'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterTop5'; ofType: null; } }; 'totalShareholders': { name: 'totalShareholders'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'unsettledShares': { name: 'unsettledShares'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'FullyDilutedUnsettledShares'; ofType: null; }; } }; }; };
    'FullyDilutedUnsettledShares': { kind: 'OBJECT'; name: 'FullyDilutedUnsettledShares'; fields: { 'amount': { name: 'amount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shares': { name: 'shares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'GICS': { kind: 'OBJECT'; name: 'GICS'; fields: { 'industries': { name: 'industries'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; 'industryGroups': { name: 'industryGroups'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; 'sectors': { name: 'sectors'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; 'subIndustries': { name: 'subIndustries'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; }; };
    'GovernanceCircularResolution': { kind: 'OBJECT'; name: 'GovernanceCircularResolution'; fields: { 'abstainCount': { name: 'abstainCount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'againstCount': { name: 'againstCount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'approvalDate': { name: 'approvalDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'approvalDocument': { name: 'approvalDocument'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'approveForm': { name: 'approveForm'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionApproveForm'; ofType: null; } }; 'boardRedraftForm': { name: 'boardRedraftForm'; type: { kind: 'OBJECT'; name: 'BoardResolutionDraftForm'; ofType: null; } }; 'circulateForm': { name: 'circulateForm'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionCirculateForm'; ofType: null; } }; 'committeeRedraftForm': { name: 'committeeRedraftForm'; type: { kind: 'OBJECT'; name: 'CommitteeCircularResolutionDraftForm'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'createdDate': { name: 'createdDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionDeleteForm'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; } }; 'forCount': { name: 'forCount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'groupId': { name: 'groupId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupType': { name: 'groupType'; type: { kind: 'ENUM'; name: 'GovernanceGroupType'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'members': { name: 'members'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionMember'; ofType: null; }; } }; 'personally': { name: 'personally'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPersonally'; ofType: null; } }; 'quorum': { name: 'quorum'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'quorumArchived': { name: 'quorumArchived'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'GovernanceRelationOption'; ofType: null; } }; 'resolutionNumber': { name: 'resolutionNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shortId': { name: 'shortId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionStatusOption'; ofType: null; } }; 'terms': { name: 'terms'; type: { kind: 'OBJECT'; name: 'GovernanceTerms'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'totalVotes': { name: 'totalVotes'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'voteForm': { name: 'voteForm'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionVoteForm'; ofType: null; } }; 'votingDeadline': { name: 'votingDeadline'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; }; };
    'GovernanceCircularResolutionApproveForm': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionApproveForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceCircularResolutionApproveTodo': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionApproveTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'deadline': { name: 'deadline'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupId': { name: 'groupId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupType': { name: 'groupType'; type: { kind: 'ENUM'; name: 'GovernanceGroupType'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'GovernanceCircularResolutionCirculateForm': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionCirculateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceCircularResolutionDeleteForm': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionDeleteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceCircularResolutionMember': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionMember'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; 'vote': { name: 'vote'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; }; };
    'GovernanceCircularResolutionPaginated': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceCircularResolution'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'GovernanceCircularResolutionPayload': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; fields: { 'resolution': { name: 'resolution'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolution'; ofType: null; } }; }; };
    'GovernanceCircularResolutionPersonally': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPersonally'; fields: { 'position': { name: 'position'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'GovernanceRelationOption'; ofType: null; } }; 'vote': { name: 'vote'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; }; };
    'GovernanceCircularResolutionStatus': { name: 'GovernanceCircularResolutionStatus'; enumValues: 'DRAFT' | 'CIRCULATING' | 'FAILED' | 'PASSED' | 'APPROVED'; };
    'GovernanceCircularResolutionStatusOption': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'GovernanceCircularResolutionStatus'; ofType: null; } }; }; };
    'GovernanceCircularResolutionVoteForm': { kind: 'OBJECT'; name: 'GovernanceCircularResolutionVoteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'vote': { name: 'vote'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; }; };
    'GovernanceCircularResolutionVoteInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceCircularResolutionVoteInput'; isOneOf: false; inputFields: [{ name: 'vote'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'GovernanceEntity': { kind: 'INTERFACE'; name: 'GovernanceEntity'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'groupId': { name: 'groupId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupType': { name: 'groupType'; type: { kind: 'ENUM'; name: 'GovernanceGroupType'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'GovernanceRelationOption'; ofType: null; } }; 'terms': { name: 'terms'; type: { kind: 'OBJECT'; name: 'GovernanceTerms'; ofType: null; } }; }; possibleTypes: 'GovernanceCircularResolution' | 'GovernanceMeeting'; };
    'GovernanceGroup': { kind: 'INTERFACE'; name: 'GovernanceGroup'; fields: { 'circularResolution': { name: 'circularResolution'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolution'; ofType: null; } }; 'circularResolutionsPaginated': { name: 'circularResolutionsPaginated'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPaginated'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'meeting': { name: 'meeting'; type: { kind: 'OBJECT'; name: 'GovernanceMeeting'; ofType: null; } }; 'meetingsPaginated': { name: 'meetingsPaginated'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPaginated'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'GovernanceRelationOption'; ofType: null; } }; 'terms': { name: 'terms'; type: { kind: 'OBJECT'; name: 'GovernanceTerms'; ofType: null; } }; }; possibleTypes: 'Board' | 'Committee'; };
    'GovernanceGroupType': { name: 'GovernanceGroupType'; enumValues: 'BOARD_OF_DIRECTORS' | 'BOARD_COMMITTEE'; };
    'GovernanceGroupTypeOption': { kind: 'OBJECT'; name: 'GovernanceGroupTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'GovernanceGroupType'; ofType: null; } }; }; };
    'GovernanceMeeting': { kind: 'OBJECT'; name: 'GovernanceMeeting'; fields: { 'approveResultForm': { name: 'approveResultForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingApproveResultForm'; ofType: null; } }; 'boardRedraftForm': { name: 'boardRedraftForm'; type: { kind: 'OBJECT'; name: 'BoardMeetingDraftForm'; ofType: null; } }; 'cancelForm': { name: 'cancelForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingCancelForm'; ofType: null; } }; 'closeVotingForm': { name: 'closeVotingForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingCloseVotingForm'; ofType: null; } }; 'committeeRedraftForm': { name: 'committeeRedraftForm'; type: { kind: 'OBJECT'; name: 'CommitteeMeetingDraftForm'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteForm'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; }; } }; 'groupId': { name: 'groupId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupType': { name: 'groupType'; type: { kind: 'ENUM'; name: 'GovernanceGroupType'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'members': { name: 'members'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingMember'; ofType: null; }; } }; 'observers': { name: 'observers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingObserver'; ofType: null; }; } }; 'onlineMeeting': { name: 'onlineMeeting'; type: { kind: 'UNION'; name: 'OnlineMeeting'; ofType: null; } }; 'openVotingForm': { name: 'openVotingForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingOpenVotingForm'; ofType: null; } }; 'personally': { name: 'personally'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPersonally'; ofType: null; } }; 'publishForm': { name: 'publishForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPublishForm'; ofType: null; } }; 'questionAskForm': { name: 'questionAskForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingAskQuestionForm'; ofType: null; } }; 'questions': { name: 'questions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingQuestion'; ofType: null; }; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'GovernanceRelationOption'; ofType: null; } }; 'reports': { name: 'reports'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; }; } }; 'resolutions': { name: 'resolutions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingResolution'; ofType: null; }; } }; 'rsvpForm': { name: 'rsvpForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingRSVPForm'; ofType: null; } }; 'secretary': { name: 'secretary'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingSecretary'; ofType: null; }; } }; 'shortId': { name: 'shortId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingStatusOption'; ofType: null; } }; 'taskAssignForm': { name: 'taskAssignForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingAssignTaskForm'; ofType: null; } }; 'tasks': { name: 'tasks'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingTask'; ofType: null; }; } }; 'terms': { name: 'terms'; type: { kind: 'OBJECT'; name: 'GovernanceTerms'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'totalAttendees': { name: 'totalAttendees'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'totalVotes': { name: 'totalVotes'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'updateMinutesForm': { name: 'updateMinutesForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingMinutesUpdateForm'; ofType: null; } }; 'uploadFileForm': { name: 'uploadFileForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingUploadFileForm'; ofType: null; } }; 'voteForm': { name: 'voteForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingVoteForm'; ofType: null; } }; }; };
    'GovernanceMeetingAnswerQuestionForm': { kind: 'OBJECT'; name: 'GovernanceMeetingAnswerQuestionForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'questionId': { name: 'questionId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingAnswerQuestionInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingAnswerQuestionInput'; isOneOf: false; inputFields: [{ name: 'questionId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingApproveResultForm': { kind: 'OBJECT'; name: 'GovernanceMeetingApproveResultForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingAskQuestionForm': { kind: 'OBJECT'; name: 'GovernanceMeetingAskQuestionForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingAskQuestionInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingAskQuestionInput'; isOneOf: false; inputFields: [{ name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingAssignTaskForm': { kind: 'OBJECT'; name: 'GovernanceMeetingAssignTaskForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'assignees': { name: 'assignees'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'dueDate': { name: 'dueDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingAssignTaskInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingAssignTaskInput'; isOneOf: false; inputFields: [{ name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'dueDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'assignees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }]; };
    'GovernanceMeetingCalendarEvent': { kind: 'OBJECT'; name: 'GovernanceMeetingCalendarEvent'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'groupId': { name: 'groupId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupType': { name: 'groupType'; type: { kind: 'ENUM'; name: 'GovernanceGroupType'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'GovernanceMeetingCancelForm': { kind: 'OBJECT'; name: 'GovernanceMeetingCancelForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'note': { name: 'note'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingCancelInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingCancelInput'; isOneOf: false; inputFields: [{ name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingChangeTaskStatusForm': { kind: 'OBJECT'; name: 'GovernanceMeetingChangeTaskStatusForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'taskId': { name: 'taskId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingChangeTaskStatusInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingChangeTaskStatusInput'; isOneOf: false; inputFields: [{ name: 'taskId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'status'; type: { kind: 'ENUM'; name: 'GovernanceMeetingTaskStatus'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingCloseVotingForm': { kind: 'OBJECT'; name: 'GovernanceMeetingCloseVotingForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingDeleteAnswerForm': { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteAnswerForm'; fields: { 'answerId': { name: 'answerId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'questionId': { name: 'questionId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingDeleteAnswerInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingDeleteAnswerInput'; isOneOf: false; inputFields: [{ name: 'questionId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'answerId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingDeleteCommentForm': { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteCommentForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'commentId': { name: 'commentId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'taskId': { name: 'taskId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingDeleteCommentInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingDeleteCommentInput'; isOneOf: false; inputFields: [{ name: 'taskId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'commentId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingDeleteFileInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingDeleteFileInput'; isOneOf: false; inputFields: [{ name: 'key'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingDeleteForm': { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingDeleteQuestionForm': { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteQuestionForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'questionId': { name: 'questionId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingDeleteQuestionInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingDeleteQuestionInput'; isOneOf: false; inputFields: [{ name: 'questionId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingDeleteTaskForm': { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteTaskForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'taskId': { name: 'taskId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingDeleteTaskInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingDeleteTaskInput'; isOneOf: false; inputFields: [{ name: 'taskId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingMember': { kind: 'OBJECT'; name: 'GovernanceMeetingMember'; fields: { 'attending': { name: 'attending'; type: { kind: 'OBJECT'; name: 'MeetingAttendingStatusOption'; ofType: null; } }; 'ebanaUser': { name: 'ebanaUser'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'proxiedBy': { name: 'proxiedBy'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingMember'; ofType: null; } }; 'rsvp': { name: 'rsvp'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; } }; }; };
    'GovernanceMeetingMinutesItemInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingMinutesItemInput'; isOneOf: false; inputFields: [{ name: 'decision'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'discussion'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingMinutesTodo': { kind: 'OBJECT'; name: 'GovernanceMeetingMinutesTodo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupId': { name: 'groupId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupType': { name: 'groupType'; type: { kind: 'ENUM'; name: 'GovernanceGroupType'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'GovernanceMeetingMinutesUpdateForm': { kind: 'OBJECT'; name: 'GovernanceMeetingMinutesUpdateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'minutes': { name: 'minutes'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'minutes_decision': { name: 'minutes_decision'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'minutes_discussion': { name: 'minutes_discussion'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingMinutesUpdateInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingMinutesUpdateInput'; isOneOf: false; inputFields: [{ name: 'minutes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingMinutesItemInput'; ofType: null; }; }; defaultValue: null }]; };
    'GovernanceMeetingObserver': { kind: 'OBJECT'; name: 'GovernanceMeetingObserver'; fields: { 'ebanaUser': { name: 'ebanaUser'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'GovernanceMeetingOpenVotingForm': { kind: 'OBJECT'; name: 'GovernanceMeetingOpenVotingForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingPaginated': { kind: 'OBJECT'; name: 'GovernanceMeetingPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeeting'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'GovernanceMeetingPayload': { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; fields: { 'meeting': { name: 'meeting'; type: { kind: 'OBJECT'; name: 'GovernanceMeeting'; ofType: null; } }; }; };
    'GovernanceMeetingPersonally': { kind: 'OBJECT'; name: 'GovernanceMeetingPersonally'; fields: { 'hasAttended': { name: 'hasAttended'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'hasVoted': { name: 'hasVoted'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'GovernanceRelationOption'; ofType: null; } }; 'rsvp': { name: 'rsvp'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'GovernanceMeetingPostCommentForm': { kind: 'OBJECT'; name: 'GovernanceMeetingPostCommentForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'taskId': { name: 'taskId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingPostCommentInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingPostCommentInput'; isOneOf: false; inputFields: [{ name: 'taskId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }]; };
    'GovernanceMeetingPublishForm': { kind: 'OBJECT'; name: 'GovernanceMeetingPublishForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingQuestion': { kind: 'OBJECT'; name: 'GovernanceMeetingQuestion'; fields: { 'answerForm': { name: 'answerForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingAnswerQuestionForm'; ofType: null; } }; 'answers': { name: 'answers'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingQuestionAnswer'; ofType: null; }; } }; 'askedBy': { name: 'askedBy'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingMember'; ofType: null; } }; 'createdDate': { name: 'createdDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteQuestionForm'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; }; };
    'GovernanceMeetingQuestionAnswer': { kind: 'OBJECT'; name: 'GovernanceMeetingQuestionAnswer'; fields: { 'answeredBy': { name: 'answeredBy'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingMember'; ofType: null; } }; 'createdDate': { name: 'createdDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteAnswerForm'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; }; };
    'GovernanceMeetingRSVPForm': { kind: 'OBJECT'; name: 'GovernanceMeetingRSVPForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'rsvp': { name: 'rsvp'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingRSVPInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingRSVPInput'; isOneOf: false; inputFields: [{ name: 'rsvp'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingResolution': { kind: 'OBJECT'; name: 'GovernanceMeetingResolution'; fields: { 'castedVotes': { name: 'castedVotes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingResolutionVoteCast'; ofType: null; }; } }; 'decision': { name: 'decision'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'discussion': { name: 'discussion'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'isStandardVote': { name: 'isStandardVote'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'majorityVote': { name: 'majorityVote'; type: { kind: 'OBJECT'; name: 'MeetingResolutionVote'; ofType: null; } }; 'myVote': { name: 'myVote'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'votes': { name: 'votes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'MeetingResolutionVote'; ofType: null; }; } }; }; };
    'GovernanceMeetingResolutionVote': { kind: 'OBJECT'; name: 'GovernanceMeetingResolutionVote'; fields: { 'isStandardVote': { name: 'isStandardVote'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'votes': { name: 'votes'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'GovernanceMeetingResolutionVoteCast': { kind: 'OBJECT'; name: 'GovernanceMeetingResolutionVoteCast'; fields: { 'member': { name: 'member'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingMember'; ofType: null; } }; 'vote': { name: 'vote'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingResolutionVote'; ofType: null; } }; }; };
    'GovernanceMeetingSecretary': { kind: 'OBJECT'; name: 'GovernanceMeetingSecretary'; fields: { 'ebanaUser': { name: 'ebanaUser'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'GovernanceMeetingSort': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingSort'; isOneOf: false; inputFields: [{ name: 'date'; type: { kind: 'ENUM'; name: 'SortDirection'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingStatus': { name: 'GovernanceMeetingStatus'; enumValues: 'DRAFT' | 'PUBLISHED' | 'OPEN_VOTING' | 'VOTING_CLOSED' | 'CANCELLED' | 'FINISHED'; };
    'GovernanceMeetingStatusOption': { kind: 'OBJECT'; name: 'GovernanceMeetingStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'GovernanceMeetingStatus'; ofType: null; } }; }; };
    'GovernanceMeetingTask': { kind: 'OBJECT'; name: 'GovernanceMeetingTask'; fields: { 'assignedBy': { name: 'assignedBy'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'assignees': { name: 'assignees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingMember'; ofType: null; }; } }; 'changeStatusForm': { name: 'changeStatusForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingChangeTaskStatusForm'; ofType: null; } }; 'commentPostForm': { name: 'commentPostForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPostCommentForm'; ofType: null; } }; 'comments': { name: 'comments'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeetingTaskComment'; ofType: null; }; } }; 'createdDate': { name: 'createdDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteTaskForm'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'dueDate': { name: 'dueDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingTaskStatusOption'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'updateForm': { name: 'updateForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingUpdateTaskForm'; ofType: null; } }; }; };
    'GovernanceMeetingTaskComment': { kind: 'OBJECT'; name: 'GovernanceMeetingTaskComment'; fields: { 'createdDate': { name: 'createdDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingDeleteCommentForm'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'postedBy': { name: 'postedBy'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingMember'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; }; };
    'GovernanceMeetingTaskStatus': { name: 'GovernanceMeetingTaskStatus'; enumValues: 'NOT_STARTED' | 'IN_PROGRESS' | 'DONE'; };
    'GovernanceMeetingTaskStatusOption': { kind: 'OBJECT'; name: 'GovernanceMeetingTaskStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'GovernanceMeetingTaskStatus'; ofType: null; } }; }; };
    'GovernanceMeetingUpdateTaskForm': { kind: 'OBJECT'; name: 'GovernanceMeetingUpdateTaskForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'assignees': { name: 'assignees'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'dueDate': { name: 'dueDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'taskId': { name: 'taskId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingUpdateTaskInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingUpdateTaskInput'; isOneOf: false; inputFields: [{ name: 'taskId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'dueDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'assignees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; }; defaultValue: null }]; };
    'GovernanceMeetingUploadFileForm': { kind: 'OBJECT'; name: 'GovernanceMeetingUploadFileForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'file': { name: 'file'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'GovernanceMeetingUploadFileInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingUploadFileInput'; isOneOf: false; inputFields: [{ name: 'file'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }]; };
    'GovernanceMeetingVoteForm': { kind: 'OBJECT'; name: 'GovernanceMeetingVoteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'votes': { name: 'votes'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'votes_vote': { name: 'votes_vote'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'GovernanceMeetingVoteInput': { kind: 'INPUT_OBJECT'; name: 'GovernanceMeetingVoteInput'; isOneOf: false; inputFields: [{ name: 'votes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'MeetingVoteInput'; ofType: null; }; }; }; defaultValue: null }]; };
    'GovernanceRelation': { name: 'GovernanceRelation'; enumValues: 'CHAIR' | 'MEMBER' | 'SECRETARY' | 'OBSERVER'; };
    'GovernanceRelationOption': { kind: 'OBJECT'; name: 'GovernanceRelationOption'; fields: { 'full': { name: 'full'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'fullyQualified': { name: 'fullyQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'qualified': { name: 'qualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'GovernanceRelation'; ofType: null; } }; }; };
    'GovernanceTerms': { kind: 'OBJECT'; name: 'GovernanceTerms'; fields: { 'chair': { name: 'chair'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'chairFull': { name: 'chairFull'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'chairFullyQualified': { name: 'chairFullyQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'chairQualified': { name: 'chairQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'group': { name: 'group'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupFull': { name: 'groupFull'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupFullyQualified': { name: 'groupFullyQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupPlural': { name: 'groupPlural'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupQualified': { name: 'groupQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupRoom': { name: 'groupRoom'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'groupShort': { name: 'groupShort'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'member': { name: 'member'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'memberFull': { name: 'memberFull'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'memberFullyQualified': { name: 'memberFullyQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'memberQualified': { name: 'memberQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'observer': { name: 'observer'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'observerFull': { name: 'observerFull'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'resolution': { name: 'resolution'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'resolutionQualified': { name: 'resolutionQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'secretary': { name: 'secretary'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'secretaryFull': { name: 'secretaryFull'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'secretaryQualified': { name: 'secretaryQualified'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'Grantee': { kind: 'OBJECT'; name: 'Grantee'; fields: { 'department': { name: 'department'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'GrantsSummary': { kind: 'OBJECT'; name: 'GrantsSummary'; fields: { 'active': { name: 'active'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'completed': { name: 'completed'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'issued': { name: 'issued'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'terminated': { name: 'terminated'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'total': { name: 'total'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'I18nString': { kind: 'OBJECT'; name: 'I18nString'; fields: { 'ar': { name: 'ar'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'en': { name: 'en'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'localized': { name: 'localized'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'I18nStringInput': { kind: 'INPUT_OBJECT'; name: 'I18nStringInput'; isOneOf: false; inputFields: [{ name: 'ar'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'en'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'ID': unknown;
    'Iban': { kind: 'OBJECT'; name: 'Iban'; fields: { 'bank': { name: 'bank'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'country': { name: 'country'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'formatted': { name: 'formatted'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'Individual': { kind: 'INTERFACE'; name: 'Individual'; fields: { 'assembliesPaginated': { name: 'assembliesPaginated'; type: { kind: 'OBJECT'; name: 'AssemblyPaginated'; ofType: null; } }; 'assembly': { name: 'assembly'; type: { kind: 'OBJECT'; name: 'Assembly'; ofType: null; } }; 'companies': { name: 'companies'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Company'; ofType: null; }; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'dashboard': { name: 'dashboard'; type: { kind: 'OBJECT'; name: 'IndividualDashboard'; ofType: null; } }; 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTrade'; ofType: null; } }; 'directTradesPaginated': { name: 'directTradesPaginated'; type: { kind: 'OBJECT'; name: 'DirectTradePaginated'; ofType: null; } }; 'equities': { name: 'equities'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INTERFACE'; name: 'Equity'; ofType: null; }; } }; 'pressRelease': { name: 'pressRelease'; type: { kind: 'OBJECT'; name: 'PressRelease'; ofType: null; } }; 'pressReleasesPaginated': { name: 'pressReleasesPaginated'; type: { kind: 'OBJECT'; name: 'PressReleasePaginated'; ofType: null; } }; 'todos': { name: 'todos'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INTERFACE'; name: 'Todo'; ofType: null; }; } }; }; possibleTypes: 'As' | 'Me'; };
    'IndividualCompaniesFilter': { name: 'IndividualCompaniesFilter'; enumValues: 'OWNS' | 'PARTICIPATES'; };
    'IndividualDashboard': { kind: 'OBJECT'; name: 'IndividualDashboard'; fields: { 'ongoingAssemblies': { name: 'ongoingAssemblies'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'ongoingBoardMeetings': { name: 'ongoingBoardMeetings'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'ongoingBoardResolutions': { name: 'ongoingBoardResolutions'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'ongoingCommitteeMeetings': { name: 'ongoingCommitteeMeetings'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'ongoingDirectTrades': { name: 'ongoingDirectTrades'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'ongoingIncentiveGrants': { name: 'ongoingIncentiveGrants'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'ongoingResolutions': { name: 'ongoingResolutions'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'totalEquities': { name: 'totalEquities'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'Int': unknown;
    'InvestorRelationsManager': { name: 'InvestorRelationsManager'; enumValues: 'EBANA' | 'SELF'; };
    'InvestorRelationsManagerOption': { kind: 'OBJECT'; name: 'InvestorRelationsManagerOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'InvestorRelationsManager'; ofType: null; } }; }; };
    'InvitePermissionForm': { kind: 'OBJECT'; name: 'InvitePermissionForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'assembly': { name: 'assembly'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardMeeting': { name: 'boardMeeting'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'capTable': { name: 'capTable'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'committees': { name: 'committees'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'esop': { name: 'esop'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'mobileNumber': { name: 'mobileNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'privileges': { name: 'privileges'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'profile': { name: 'profile'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'relation': { name: 'relation'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'subscription': { name: 'subscription'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'userId': { name: 'userId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'InvitePermissionInput': { kind: 'INPUT_OBJECT'; name: 'InvitePermissionInput'; isOneOf: false; inputFields: [{ name: 'userId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'relation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'mobileNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'capTable'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'assembly'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'profile'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'esop'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'boardMeeting'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'committees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'privileges'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'subscription'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }]; };
    'Invoice': { kind: 'OBJECT'; name: 'Invoice'; fields: { 'amount': { name: 'amount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'pdf': { name: 'pdf'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; }; };
    'ItemOption': { kind: 'OBJECT'; name: 'ItemOption'; fields: { 'number': { name: 'number'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'option': { name: 'option'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'percent': { name: 'percent'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'total': { name: 'total'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'JSON': unknown;
    'JustificationForm': { kind: 'OBJECT'; name: 'JustificationForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'reason': { name: 'reason'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'JustificationInput': { kind: 'INPUT_OBJECT'; name: 'JustificationInput'; isOneOf: false; inputFields: [{ name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'Kpi': { kind: 'OBJECT'; name: 'Kpi'; fields: { 'text': { name: 'text'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'LegalPerson': { kind: 'OBJECT'; name: 'LegalPerson'; fields: { 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; }; };
    'LegalPersonIdentifier': { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; fields: { 'encoded': { name: 'encoded'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'inputType': { name: 'inputType'; type: { kind: 'ENUM'; name: 'LegalPersonIdentifierInputType'; ofType: null; } }; 'inputTypeName': { name: 'inputTypeName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LegalPersonIdentifierType'; ofType: null; }; } }; 'typeName': { name: 'typeName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'LegalPersonIdentifierInput': { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; isOneOf: false; inputFields: [{ name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'ENUM'; name: 'LegalPersonIdentifierInputType'; ofType: null; }; defaultValue: null }]; };
    'LegalPersonIdentifierInputType': { name: 'LegalPersonIdentifierInputType'; enumValues: 'NATURAL_SAUDI_GCC' | 'SAUDI_CR' | 'UNIFIED_NATIONAL_NUMBER' | 'FOREIGN_NATURAL_PERSON' | 'FOREIGN_LEGAL_ENTITY'; };
    'LegalPersonIdentifierInputTypeOption': { kind: 'OBJECT'; name: 'LegalPersonIdentifierInputTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'LegalPersonIdentifierInputType'; ofType: null; } }; }; };
    'LegalPersonIdentifierType': { name: 'LegalPersonIdentifierType'; enumValues: 'NID' | 'IQAMA' | 'EMIRATES_ID' | 'KUWAIT_CIVIL_ID' | 'QATARI_ID' | 'BAHRAIN_PERSONAL_NUMBER' | 'CR' | 'UNIFIED_NATIONAL_NUMBER' | 'FOREIGN_NATURAL_PERSON' | 'FOREIGN_LEGAL_ENTITY' | 'UNKNOWN'; };
    'LegalPersonIdentifierTypeOption': { kind: 'OBJECT'; name: 'LegalPersonIdentifierTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'LegalPersonIdentifierType'; ofType: null; } }; }; };
    'LiquidationPreference': { kind: 'OBJECT'; name: 'LiquidationPreference'; fields: { 'liquidationMultiple': { name: 'liquidationMultiple'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'participationMultiple': { name: 'participationMultiple'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'seniority': { name: 'seniority'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'ListFormField': { kind: 'OBJECT'; name: 'ListFormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'LiveKitOnlineMeeting': { kind: 'OBJECT'; name: 'LiveKitOnlineMeeting'; fields: { 'accessToken': { name: 'accessToken'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'availableUntil': { name: 'availableUntil'; type: { kind: 'SCALAR'; name: 'OffsetDateTime'; ofType: null; } }; 'room': { name: 'room'; type: { kind: 'OBJECT'; name: 'LiveKitRoom'; ofType: null; } }; 'roomName': { name: 'roomName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'serverUrl': { name: 'serverUrl'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'LiveKitRoom': { kind: 'OBJECT'; name: 'LiveKitRoom'; fields: { 'host': { name: 'host'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'participants': { name: 'participants'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'LiveKitRoomParticipant'; ofType: null; }; } }; 'sid': { name: 'sid'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'token': { name: 'token'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'LiveKitRoomGrantRoomAdminInput': { kind: 'INPUT_OBJECT'; name: 'LiveKitRoomGrantRoomAdminInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'LiveKitRoomKickParticipantInput': { kind: 'INPUT_OBJECT'; name: 'LiveKitRoomKickParticipantInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'LiveKitRoomMuteParticipantInput': { kind: 'INPUT_OBJECT'; name: 'LiveKitRoomMuteParticipantInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'mute'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'LiveKitRoomParticipant': { kind: 'OBJECT'; name: 'LiveKitRoomParticipant'; fields: { 'sid': { name: 'sid'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'LiveKitRoomPayload': { kind: 'OBJECT'; name: 'LiveKitRoomPayload'; fields: { 'room': { name: 'room'; type: { kind: 'OBJECT'; name: 'LiveKitRoom'; ofType: null; } }; }; };
    'LocalDate': unknown;
    'LocalDateElements': { kind: 'OBJECT'; name: 'LocalDateElements'; fields: { 'day': { name: 'day'; type: { kind: 'OBJECT'; name: 'DayOfWeekOption'; ofType: null; } }; 'dayOfMonth': { name: 'dayOfMonth'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'dayOfWeek': { name: 'dayOfWeek'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'default': { name: 'default'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'full': { name: 'full'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'iso': { name: 'iso'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'long': { name: 'long'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'medium': { name: 'medium'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'month': { name: 'month'; type: { kind: 'OBJECT'; name: 'MonthOption'; ofType: null; } }; 'monthOfYear': { name: 'monthOfYear'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'quarter': { name: 'quarter'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'relative': { name: 'relative'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'relativeLong': { name: 'relativeLong'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'relativeShort': { name: 'relativeShort'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'short': { name: 'short'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'year': { name: 'year'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; }; };
    'LocalDateTime': unknown;
    'LocalDateTimeElements': { kind: 'OBJECT'; name: 'LocalDateTimeElements'; fields: { 'ampm': { name: 'ampm'; type: { kind: 'OBJECT'; name: 'AmPmOption'; ofType: null; } }; 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'day': { name: 'day'; type: { kind: 'OBJECT'; name: 'DayOfWeekOption'; ofType: null; } }; 'dayOfMonth': { name: 'dayOfMonth'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'dayOfWeek': { name: 'dayOfWeek'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'default': { name: 'default'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'full': { name: 'full'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'hour': { name: 'hour'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'hourOfAmPm': { name: 'hourOfAmPm'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'iso': { name: 'iso'; type: { kind: 'SCALAR'; name: 'LocalDateTime'; ofType: null; } }; 'long': { name: 'long'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'medium': { name: 'medium'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'minute': { name: 'minute'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'month': { name: 'month'; type: { kind: 'OBJECT'; name: 'MonthOption'; ofType: null; } }; 'monthOfYear': { name: 'monthOfYear'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'quarter': { name: 'quarter'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'relative': { name: 'relative'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'relativeLong': { name: 'relativeLong'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'relativeSeconds': { name: 'relativeSeconds'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'relativeShort': { name: 'relativeShort'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'second': { name: 'second'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'short': { name: 'short'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'time': { name: 'time'; type: { kind: 'OBJECT'; name: 'LocalTimeElements'; ofType: null; } }; 'year': { name: 'year'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; }; };
    'LocalTime': unknown;
    'LocalTimeElements': { kind: 'OBJECT'; name: 'LocalTimeElements'; fields: { 'ampm': { name: 'ampm'; type: { kind: 'OBJECT'; name: 'AmPmOption'; ofType: null; } }; 'default': { name: 'default'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'full': { name: 'full'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'hour': { name: 'hour'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'hourOfAmPm': { name: 'hourOfAmPm'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'iso': { name: 'iso'; type: { kind: 'SCALAR'; name: 'LocalTime'; ofType: null; } }; 'long': { name: 'long'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'medium': { name: 'medium'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'minute': { name: 'minute'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'second': { name: 'second'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'short': { name: 'short'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'Me': { kind: 'OBJECT'; name: 'Me'; fields: { 'assembliesPaginated': { name: 'assembliesPaginated'; type: { kind: 'OBJECT'; name: 'AssemblyPaginated'; ofType: null; } }; 'assembly': { name: 'assembly'; type: { kind: 'OBJECT'; name: 'Assembly'; ofType: null; } }; 'board': { name: 'board'; type: { kind: 'OBJECT'; name: 'Board'; ofType: null; } }; 'boards': { name: 'boards'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Board'; ofType: null; }; } }; 'calendar': { name: 'calendar'; type: { kind: 'OBJECT'; name: 'Calendar'; ofType: null; } }; 'commercialRegistrationLookupForm': { name: 'commercialRegistrationLookupForm'; type: { kind: 'OBJECT'; name: 'CommercialRegistrationLookupForm'; ofType: null; } }; 'committee': { name: 'committee'; type: { kind: 'OBJECT'; name: 'Committee'; ofType: null; } }; 'committees': { name: 'committees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Committee'; ofType: null; }; } }; 'companies': { name: 'companies'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Company'; ofType: null; }; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'dashboard': { name: 'dashboard'; type: { kind: 'OBJECT'; name: 'IndividualDashboard'; ofType: null; } }; 'directTrade': { name: 'directTrade'; type: { kind: 'OBJECT'; name: 'DirectTrade'; ofType: null; } }; 'directTradesPaginated': { name: 'directTradesPaginated'; type: { kind: 'OBJECT'; name: 'DirectTradePaginated'; ofType: null; } }; 'eig': { name: 'eig'; type: { kind: 'OBJECT'; name: 'EIG'; ofType: null; } }; 'eigPaginated': { name: 'eigPaginated'; type: { kind: 'OBJECT'; name: 'EIGPaginated'; ofType: null; } }; 'eip': { name: 'eip'; type: { kind: 'OBJECT'; name: 'EIP'; ofType: null; } }; 'equities': { name: 'equities'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'MeEquity'; ofType: null; }; } }; 'equity': { name: 'equity'; type: { kind: 'OBJECT'; name: 'MeEquity'; ofType: null; } }; 'exercisedSummary': { name: 'exercisedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'governance': { name: 'governance'; type: { kind: 'OBJECT'; name: 'MeGovernance'; ofType: null; } }; 'nonVestedSummary': { name: 'nonVestedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'personalInformation': { name: 'personalInformation'; type: { kind: 'OBJECT'; name: 'PersonalInformation'; ofType: null; } }; 'personalInformationUpdateForm': { name: 'personalInformationUpdateForm'; type: { kind: 'OBJECT'; name: 'PersonalInformationUpdateForm'; ofType: null; } }; 'portfolioValue': { name: 'portfolioValue'; type: { kind: 'OBJECT'; name: 'StockTicker'; ofType: null; } }; 'pressRelease': { name: 'pressRelease'; type: { kind: 'OBJECT'; name: 'PressRelease'; ofType: null; } }; 'pressReleasesPaginated': { name: 'pressReleasesPaginated'; type: { kind: 'OBJECT'; name: 'PressReleasePaginated'; ofType: null; } }; 'settledSummary': { name: 'settledSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'todos': { name: 'todos'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INTERFACE'; name: 'Todo'; ofType: null; }; } }; 'vestedSummary': { name: 'vestedSummary'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'MeEquity': { kind: 'OBJECT'; name: 'MeEquity'; fields: { 'agent': { name: 'agent'; type: { kind: 'OBJECT'; name: 'EquityAgent'; ofType: null; } }; 'averageCostPerShares': { name: 'averageCostPerShares'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'certificate': { name: 'certificate'; type: { kind: 'OBJECT'; name: 'EquityCertificate'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'marketValue': { name: 'marketValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'policyAccepted': { name: 'policyAccepted'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'position': { name: 'position'; type: { kind: 'OBJECT'; name: 'EquityPosition'; ofType: null; } }; 'positions': { name: 'positions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'EquityPosition'; ofType: null; }; } }; 'profitAndLoss': { name: 'profitAndLoss'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'profitAndLossPercent': { name: 'profitAndLossPercent'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'recentRecipients': { name: 'recentRecipients'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; }; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'totalCost': { name: 'totalCost'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'totalOwnership': { name: 'totalOwnership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'totalShares': { name: 'totalShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'tradingPolicy': { name: 'tradingPolicy'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; }; };
    'MeGovernance': { kind: 'OBJECT'; name: 'MeGovernance'; fields: { 'board': { name: 'board'; type: { kind: 'OBJECT'; name: 'Board'; ofType: null; } }; 'boards': { name: 'boards'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Board'; ofType: null; }; } }; 'circulatingResolutions': { name: 'circulatingResolutions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceCircularResolution'; ofType: null; }; } }; 'committee': { name: 'committee'; type: { kind: 'OBJECT'; name: 'Committee'; ofType: null; } }; 'committees': { name: 'committees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Committee'; ofType: null; }; } }; 'ongoingMeeting': { name: 'ongoingMeeting'; type: { kind: 'OBJECT'; name: 'GovernanceMeeting'; ofType: null; } }; 'upcomingMeetings': { name: 'upcomingMeetings'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'GovernanceMeeting'; ofType: null; }; } }; }; };
    'Meeting': { kind: 'INTERFACE'; name: 'Meeting'; fields: { 'date': { name: 'date'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'files': { name: 'files'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'onlineMeeting': { name: 'onlineMeeting'; type: { kind: 'UNION'; name: 'OnlineMeeting'; ofType: null; } }; 'reports': { name: 'reports'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; }; }; } }; 'resolutions': { name: 'resolutions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INTERFACE'; name: 'MeetingResolution'; ofType: null; }; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'totalAttendees': { name: 'totalAttendees'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'totalVotes': { name: 'totalVotes'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; possibleTypes: never; };
    'MeetingAttendingStatus': { name: 'MeetingAttendingStatus'; enumValues: 'PRESENT' | 'ABSENT' | 'RSVP_YES' | 'RSVP_NO' | 'PROXY' | 'UNCONFIRMED'; };
    'MeetingAttendingStatusOption': { kind: 'OBJECT'; name: 'MeetingAttendingStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'MeetingAttendingStatus'; ofType: null; } }; }; };
    'MeetingResolution': { kind: 'INTERFACE'; name: 'MeetingResolution'; fields: { 'decision': { name: 'decision'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'discussion': { name: 'discussion'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'isStandardVote': { name: 'isStandardVote'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'majorityVote': { name: 'majorityVote'; type: { kind: 'OBJECT'; name: 'MeetingResolutionVote'; ofType: null; } }; 'myVote': { name: 'myVote'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'text': { name: 'text'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'votes': { name: 'votes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'MeetingResolutionVote'; ofType: null; }; } }; }; possibleTypes: never; };
    'MeetingResolutionVote': { kind: 'OBJECT'; name: 'MeetingResolutionVote'; fields: { 'isStandardVote': { name: 'isStandardVote'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'votes': { name: 'votes'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'MeetingVoteInput': { kind: 'INPUT_OBJECT'; name: 'MeetingVoteInput'; isOneOf: false; inputFields: [{ name: 'vote'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'Money': { kind: 'OBJECT'; name: 'Money'; fields: { 'compact': { name: 'compact'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'formatted': { name: 'formatted'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; }; };
    'Month': { name: 'Month'; enumValues: 'JANUARY' | 'FEBRUARY' | 'MARCH' | 'APRIL' | 'MAY' | 'JUNE' | 'JULY' | 'AUGUST' | 'SEPTEMBER' | 'OCTOBER' | 'NOVEMBER' | 'DECEMBER'; };
    'MonthOption': { kind: 'OBJECT'; name: 'MonthOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'Month'; ofType: null; } }; }; };
    'MoyasarApplePaymentForm': { kind: 'OBJECT'; name: 'MoyasarApplePaymentForm'; fields: { 'completeUrl': { name: 'completeUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'form': { name: 'form'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'initiate': { name: 'initiate'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'initiateUrl': { name: 'initiateUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'paymentUrl': { name: 'paymentUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'request': { name: 'request'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'MoyasarCardPayment': { kind: 'OBJECT'; name: 'MoyasarCardPayment'; fields: { 'completeUrl': { name: 'completeUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'form': { name: 'form'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'initiateUrl': { name: 'initiateUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; }; };
    'MoyasarPaymentForm': { kind: 'OBJECT'; name: 'MoyasarPaymentForm'; fields: { 'amount': { name: 'amount'; type: { kind: 'OBJECT'; name: 'PaymentAmount'; ofType: null; } }; 'apple': { name: 'apple'; type: { kind: 'OBJECT'; name: 'MoyasarApplePaymentForm'; ofType: null; } }; 'card': { name: 'card'; type: { kind: 'OBJECT'; name: 'MoyasarCardPayment'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; }; };
    'MoyasarTokenizeCardForm': { kind: 'OBJECT'; name: 'MoyasarTokenizeCardForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'callback_url': { name: 'callback_url'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'cvc': { name: 'cvc'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'month': { name: 'month'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'number': { name: 'number'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'publishable_api_key': { name: 'publishable_api_key'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'save_only': { name: 'save_only'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'year': { name: 'year'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'MoyasarTokenizeCardInput': { kind: 'INPUT_OBJECT'; name: 'MoyasarTokenizeCardInput'; isOneOf: false; inputFields: [{ name: 'publishable_api_key'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'save_only'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'callback_url'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'metadata'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'number'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'year'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'month'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'cvc'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'Mutation': { kind: 'OBJECT'; name: 'Mutation'; fields: { 'addonPurchase': { name: 'addonPurchase'; type: { kind: 'OBJECT'; name: 'AddonOrderPayload'; ofType: null; } }; 'assemblyAmend': { name: 'assemblyAmend'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyAttend': { name: 'assemblyAttend'; type: { kind: 'OBJECT'; name: 'AssemblyPayload'; ofType: null; } }; 'assemblyCancel': { name: 'assemblyCancel'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyCloseVoting': { name: 'assemblyCloseVoting'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyCreate': { name: 'assemblyCreate'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyDelete': { name: 'assemblyDelete'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyDeleteFile': { name: 'assemblyDeleteFile'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyFinish': { name: 'assemblyFinish'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyOpenVoting': { name: 'assemblyOpenVoting'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyPostpone': { name: 'assemblyPostpone'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyPublish': { name: 'assemblyPublish'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblySubmitVote': { name: 'assemblySubmitVote'; type: { kind: 'OBJECT'; name: 'AssemblyPayload'; ofType: null; } }; 'assemblyUpdate': { name: 'assemblyUpdate'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyUpdateMinutes': { name: 'assemblyUpdateMinutes'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assemblyUploadFile': { name: 'assemblyUploadFile'; type: { kind: 'OBJECT'; name: 'AssemblyManagementPayload'; ofType: null; } }; 'assignPermission': { name: 'assignPermission'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'billingInformationUpdate': { name: 'billingInformationUpdate'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'boardMeetingCreate': { name: 'boardMeetingCreate'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'boardMeetingUpdate': { name: 'boardMeetingUpdate'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'boardResolutionDraft': { name: 'boardResolutionDraft'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; ofType: null; } }; 'boardResolutionRedraft': { name: 'boardResolutionRedraft'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; ofType: null; } }; 'boardSetup': { name: 'boardSetup'; type: { kind: 'OBJECT'; name: 'BoardSetupPayload'; ofType: null; } }; 'commercialRegistrationLookup': { name: 'commercialRegistrationLookup'; type: { kind: 'OBJECT'; name: 'CommercialRegistrationLookup'; ofType: null; } }; 'committeeCircularResolutionDraft': { name: 'committeeCircularResolutionDraft'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; ofType: null; } }; 'committeeCircularResolutionRedraft': { name: 'committeeCircularResolutionRedraft'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; ofType: null; } }; 'committeeCreate': { name: 'committeeCreate'; type: { kind: 'OBJECT'; name: 'CommitteePayload'; ofType: null; } }; 'committeeMeetingCreate': { name: 'committeeMeetingCreate'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'committeeMeetingUpdate': { name: 'committeeMeetingUpdate'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'committeeUpdate': { name: 'committeeUpdate'; type: { kind: 'OBJECT'; name: 'CommitteePayload'; ofType: null; } }; 'companyAuthorizeAgent': { name: 'companyAuthorizeAgent'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'companyDelegationCreate': { name: 'companyDelegationCreate'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'companyDelegationRevoke': { name: 'companyDelegationRevoke'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'companyDelegationUpdate': { name: 'companyDelegationUpdate'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'companyDismissInsight': { name: 'companyDismissInsight'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'companyTerminateAgency': { name: 'companyTerminateAgency'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'companyUpdateDefaultCard': { name: 'companyUpdateDefaultCard'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'directGiftCreate': { name: 'directGiftCreate'; type: { kind: 'OBJECT'; name: 'DirectGiftCreatePayload'; ofType: null; } }; 'directSellCreate': { name: 'directSellCreate'; type: { kind: 'OBJECT'; name: 'DirectSellCreatePayload'; ofType: null; } }; 'directTradeAccept': { name: 'directTradeAccept'; type: { kind: 'OBJECT'; name: 'DirectTradeAcceptPayload'; ofType: null; } }; 'directTradeCancel': { name: 'directTradeCancel'; type: { kind: 'OBJECT'; name: 'DirectTradeManagementPayload'; ofType: null; } }; 'directTradeConfirm': { name: 'directTradeConfirm'; type: { kind: 'OBJECT'; name: 'DirectTradeManagementPayload'; ofType: null; } }; 'directTradeDelete': { name: 'directTradeDelete'; type: { kind: 'OBJECT'; name: 'DirectTradePayload'; ofType: null; } }; 'directTradePayment': { name: 'directTradePayment'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'directTradeReject': { name: 'directTradeReject'; type: { kind: 'OBJECT'; name: 'DirectTradePayload'; ofType: null; } }; 'eigAcceptOffer': { name: 'eigAcceptOffer'; type: { kind: 'OBJECT'; name: 'EIGPayload'; ofType: null; } }; 'eigApproveExercise': { name: 'eigApproveExercise'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigCreate': { name: 'eigCreate'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigDelete': { name: 'eigDelete'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigExercise': { name: 'eigExercise'; type: { kind: 'OBJECT'; name: 'EIGPayload'; ofType: null; } }; 'eigReferByApprover': { name: 'eigReferByApprover'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigReferByGrantee': { name: 'eigReferByGrantee'; type: { kind: 'OBJECT'; name: 'EIGPayload'; ofType: null; } }; 'eigRejectExercise': { name: 'eigRejectExercise'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigSendOffer': { name: 'eigSendOffer'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigSettle': { name: 'eigSettle'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigSubmit': { name: 'eigSubmit'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigTerminate': { name: 'eigTerminate'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eigUpdate': { name: 'eigUpdate'; type: { kind: 'OBJECT'; name: 'EIGManagementPayload'; ofType: null; } }; 'eipCreate': { name: 'eipCreate'; type: { kind: 'OBJECT'; name: 'EIPManagementPayload'; ofType: null; } }; 'eipUpdate': { name: 'eipUpdate'; type: { kind: 'OBJECT'; name: 'EIPManagementPayload'; ofType: null; } }; 'fakeMutation': { name: 'fakeMutation'; type: { kind: 'OBJECT'; name: 'FakeFormPayload'; ofType: null; } }; 'governanceCircularResolutionApprove': { name: 'governanceCircularResolutionApprove'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; ofType: null; } }; 'governanceCircularResolutionCirculate': { name: 'governanceCircularResolutionCirculate'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; ofType: null; } }; 'governanceCircularResolutionDelete': { name: 'governanceCircularResolutionDelete'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; ofType: null; } }; 'governanceCircularResolutionVote': { name: 'governanceCircularResolutionVote'; type: { kind: 'OBJECT'; name: 'GovernanceCircularResolutionPayload'; ofType: null; } }; 'governanceMeetingAnswerQuestion': { name: 'governanceMeetingAnswerQuestion'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingApproveResult': { name: 'governanceMeetingApproveResult'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingAskQuestion': { name: 'governanceMeetingAskQuestion'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingAssignTask': { name: 'governanceMeetingAssignTask'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingCancel': { name: 'governanceMeetingCancel'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingChangeTaskStatus': { name: 'governanceMeetingChangeTaskStatus'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingCloseVoting': { name: 'governanceMeetingCloseVoting'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingDelete': { name: 'governanceMeetingDelete'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingDeleteAnswer': { name: 'governanceMeetingDeleteAnswer'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingDeleteComment': { name: 'governanceMeetingDeleteComment'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingDeleteFile': { name: 'governanceMeetingDeleteFile'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingDeleteQuestion': { name: 'governanceMeetingDeleteQuestion'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingDeleteTask': { name: 'governanceMeetingDeleteTask'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingOpenVoting': { name: 'governanceMeetingOpenVoting'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingPostComment': { name: 'governanceMeetingPostComment'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingPublish': { name: 'governanceMeetingPublish'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingRSVP': { name: 'governanceMeetingRSVP'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingUpdateMinutes': { name: 'governanceMeetingUpdateMinutes'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingUpdateTask': { name: 'governanceMeetingUpdateTask'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingUploadFile': { name: 'governanceMeetingUploadFile'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'governanceMeetingVote': { name: 'governanceMeetingVote'; type: { kind: 'OBJECT'; name: 'GovernanceMeetingPayload'; ofType: null; } }; 'invitePermission': { name: 'invitePermission'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'livekitRoomGrantRoomAdmin': { name: 'livekitRoomGrantRoomAdmin'; type: { kind: 'OBJECT'; name: 'LiveKitRoomPayload'; ofType: null; } }; 'livekitRoomKickParticipant': { name: 'livekitRoomKickParticipant'; type: { kind: 'OBJECT'; name: 'LiveKitRoomPayload'; ofType: null; } }; 'livekitRoomMuteParticipant': { name: 'livekitRoomMuteParticipant'; type: { kind: 'OBJECT'; name: 'LiveKitRoomPayload'; ofType: null; } }; 'moyasarTokenize': { name: 'moyasarTokenize'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'paymentCardDelete': { name: 'paymentCardDelete'; type: { kind: 'OBJECT'; name: 'PaymentCardDeletePayload'; ofType: null; } }; 'personalInformationUpdate': { name: 'personalInformationUpdate'; type: { kind: 'OBJECT'; name: 'PersonalInformationPayload'; ofType: null; } }; 'pressReleaseCreate': { name: 'pressReleaseCreate'; type: { kind: 'OBJECT'; name: 'PressReleasePayload'; ofType: null; } }; 'registerAddCommon': { name: 'registerAddCommon'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerAddPreferred': { name: 'registerAddPreferred'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerAddRedeemable': { name: 'registerAddRedeemable'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerBatchMoveStock': { name: 'registerBatchMoveStock'; type: { kind: 'OBJECT'; name: 'RegisterBatchMoveStockMovePayload'; ofType: null; } }; 'registerChangeParValue': { name: 'registerChangeParValue'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerChangeParValuePreview': { name: 'registerChangeParValuePreview'; type: { kind: 'OBJECT'; name: 'RegisterChangeParValuePreview'; ofType: null; } }; 'registerCompany': { name: 'registerCompany'; type: { kind: 'OBJECT'; name: 'RegisterCompanyPayload'; ofType: null; } }; 'registerIssueBonusShares': { name: 'registerIssueBonusShares'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerIssueBonusSharesPreview': { name: 'registerIssueBonusSharesPreview'; type: { kind: 'OBJECT'; name: 'RegisterIssueBonusSharesPreview'; ofType: null; } }; 'registerIssueShares': { name: 'registerIssueShares'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerIssueSharesPreview': { name: 'registerIssueSharesPreview'; type: { kind: 'OBJECT'; name: 'RegisterIssueSharesPreview'; ofType: null; } }; 'registerLockTrading': { name: 'registerLockTrading'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerMoveStock': { name: 'registerMoveStock'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerRemoveShareClass': { name: 'registerRemoveShareClass'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerUnlockTrading': { name: 'registerUnlockTrading'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'registerUpdateShareholder': { name: 'registerUpdateShareholder'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; ofType: null; } }; 'revokePermissions': { name: 'revokePermissions'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'safeCreate': { name: 'safeCreate'; type: { kind: 'OBJECT'; name: 'SafePayload'; ofType: null; } }; 'setupCapTable': { name: 'setupCapTable'; type: { kind: 'OBJECT'; name: 'SetupCapTablePayload'; ofType: null; } }; 'subscriptionCreate': { name: 'subscriptionCreate'; type: { kind: 'OBJECT'; name: 'CompanySubscriptionPayload'; ofType: null; } }; 'subscriptionPay': { name: 'subscriptionPay'; type: { kind: 'OBJECT'; name: 'PaymentPayPayload'; ofType: null; } }; 'subscriptionRenew': { name: 'subscriptionRenew'; type: { kind: 'OBJECT'; name: 'CompanySubscriptionPayload'; ofType: null; } }; 'subscriptionUpgrade': { name: 'subscriptionUpgrade'; type: { kind: 'OBJECT'; name: 'CompanySubscriptionPayload'; ofType: null; } }; 'updateCompany': { name: 'updateCompany'; type: { kind: 'OBJECT'; name: 'CompanyManagementPayload'; ofType: null; } }; 'updateShareholdersFitStatus': { name: 'updateShareholdersFitStatus'; type: { kind: 'OBJECT'; name: 'ShareholderRegister'; ofType: null; } }; }; };
    'NaturalPersonIdentifier': { kind: 'OBJECT'; name: 'NaturalPersonIdentifier'; fields: { 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'NominationAmendInput': { kind: 'INPUT_OBJECT'; name: 'NominationAmendInput'; isOneOf: false; inputFields: [{ name: 'nominees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyAmendNomineeInput'; ofType: null; }; }; defaultValue: "[]" }, { name: 'blockedIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; }; defaultValue: null }]; };
    'NominationCreateInput': { kind: 'INPUT_OBJECT'; name: 'NominationCreateInput'; isOneOf: false; inputFields: [{ name: 'nominees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyNomineeCreateInput'; ofType: null; }; }; defaultValue: "[]" }, { name: 'blockedIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; }; defaultValue: null }]; };
    'NominationUpdateInput': { kind: 'INPUT_OBJECT'; name: 'NominationUpdateInput'; isOneOf: false; inputFields: [{ name: 'nominees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssemblyUpdateNomineeInput'; ofType: null; }; }; defaultValue: "[]" }, { name: 'blockedIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; }; defaultValue: null }]; };
    'NomineeResultSummary': { kind: 'OBJECT'; name: 'NomineeResultSummary'; fields: { 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'percentageOfElectionVotes': { name: 'percentageOfElectionVotes'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'Number': { kind: 'OBJECT'; name: 'Number'; fields: { 'compact': { name: 'compact'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'formatted': { name: 'formatted'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; }; };
    'NumberFormField': { kind: 'OBJECT'; name: 'NumberFormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'maxFractionalDigits': { name: 'maxFractionalDigits'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'NumberToTotal': { kind: 'OBJECT'; name: 'NumberToTotal'; fields: { 'n': { name: 'n'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'p': { name: 'p'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'r': { name: 'r'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 't': { name: 't'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'OffsetDateTime': unknown;
    'OnlineMeeting': { kind: 'UNION'; name: 'OnlineMeeting'; fields: {}; possibleTypes: 'ExternalOnlineMeeting' | 'LiveKitOnlineMeeting'; };
    'OnlineMeetingInput': { kind: 'INPUT_OBJECT'; name: 'OnlineMeetingInput'; isOneOf: false; inputFields: [{ name: 'external'; type: { kind: 'INPUT_OBJECT'; name: 'ExternalMeetingInput'; ofType: null; }; defaultValue: null }, { name: 'ebanaRoom'; type: { kind: 'INPUT_OBJECT'; name: 'EbanaRoomMeetingInput'; ofType: null; }; defaultValue: null }]; };
    'Option': { kind: 'INTERFACE'; name: 'Option'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; possibleTypes: 'AmPmOption' | 'AssemblyAnswerOption' | 'AssemblyChairRelationOption' | 'AssemblyStatusOption' | 'AssemblyStatusesOption' | 'AssemblyTypeOption' | 'AwardTypeOption' | 'BoardDirectorTypeOption' | 'BusinessTypeOption' | 'CommitteeMemberTypeOption' | 'CompanyPlanAddon' | 'CompanyRegistrationLegalCapacityOption' | 'CompanySubscriptionStatusOption' | 'Country' | 'DayOfWeekOption' | 'DirectTradeStatusOption' | 'DirectTradeTypeOption' | 'EIGStatusOption' | 'EIGStatusesOption' | 'EipStatusOption' | 'FakeMutationSelectOption' | 'GovernanceCircularResolutionStatusOption' | 'GovernanceGroupTypeOption' | 'GovernanceMeetingStatusOption' | 'GovernanceMeetingTaskStatusOption' | 'GovernanceRelationOption' | 'InvestorRelationsManagerOption' | 'LegalPersonIdentifierInputTypeOption' | 'LegalPersonIdentifierTypeOption' | 'MeetingAttendingStatusOption' | 'MonthOption' | 'PaymentCardBrandInfo' | 'PermissionOption' | 'RestrictionKindOption' | 'SafeTypeOption' | 'SocialNetworkOption' | 'StringOption' | 'TermLengthOption' | 'UserOccupationOption' | 'VestingScheduleTermStatusOption'; };
    'OwnershipInput': { kind: 'INPUT_OBJECT'; name: 'OwnershipInput'; isOneOf: false; inputFields: [{ name: 'prefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'count'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'shareClassName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'PageInfo': { kind: 'OBJECT'; name: 'PageInfo'; fields: { 'endCursor': { name: 'endCursor'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'hasNextPage': { name: 'hasNextPage'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'hasPreviousPage': { name: 'hasPreviousPage'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'startCursor': { name: 'startCursor'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'PaymentAmount': { kind: 'OBJECT'; name: 'PaymentAmount'; fields: { 'tax': { name: 'tax'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'total': { name: 'total'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'untaxed': { name: 'untaxed'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'PaymentCard': { kind: 'OBJECT'; name: 'PaymentCard'; fields: { 'brand': { name: 'brand'; type: { kind: 'OBJECT'; name: 'PaymentCardBrandInfo'; ofType: null; } }; 'defaultCard': { name: 'defaultCard'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'deleteForm': { name: 'deleteForm'; type: { kind: 'OBJECT'; name: 'PaymentCardDeleteForm'; ofType: null; } }; 'funding': { name: 'funding'; type: { kind: 'ENUM'; name: 'PaymentCardFunding'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'lastFour': { name: 'lastFour'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'month': { name: 'month'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'setDefaultCardForm': { name: 'setDefaultCardForm'; type: { kind: 'OBJECT'; name: 'CompanyUpdateDefaultCardForm'; ofType: null; } }; 'year': { name: 'year'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'PaymentCardBrand': { name: 'PaymentCardBrand'; enumValues: 'VISA' | 'MASTER' | 'MADA' | 'AMEX'; };
    'PaymentCardBrandInfo': { kind: 'OBJECT'; name: 'PaymentCardBrandInfo'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'PaymentCardBrand'; ofType: null; } }; }; };
    'PaymentCardDeleteForm': { kind: 'OBJECT'; name: 'PaymentCardDeleteForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'PaymentCardDeleteInput': { kind: 'INPUT_OBJECT'; name: 'PaymentCardDeleteInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'PaymentCardDeletePayload': { kind: 'OBJECT'; name: 'PaymentCardDeletePayload'; fields: { 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; }; };
    'PaymentCardFunding': { name: 'PaymentCardFunding'; enumValues: 'CREDIT' | 'DEBIT'; };
    'PaymentPayInput': { kind: 'INPUT_OBJECT'; name: 'PaymentPayInput'; isOneOf: false; inputFields: [{ name: 'redirectUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; }; defaultValue: null }]; };
    'PaymentPayPayload': { kind: 'OBJECT'; name: 'PaymentPayPayload'; fields: { 'moyasar': { name: 'moyasar'; type: { kind: 'OBJECT'; name: 'MoyasarPaymentForm'; ofType: null; } }; }; };
    'Percentage': { kind: 'OBJECT'; name: 'Percentage'; fields: { 'complement': { name: 'complement'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; 'complementFormatted': { name: 'complementFormatted'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'formatted': { name: 'formatted'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; }; };
    'Period': { kind: 'OBJECT'; name: 'Period'; fields: { 'days': { name: 'days'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'formatted': { name: 'formatted'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'months': { name: 'months'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'years': { name: 'years'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; }; };
    'Permission': { name: 'Permission'; enumValues: 'ASSEMBLY_MANAGEMENT' | 'ASSEMBLY_DRAFTING' | 'ASSEMBLY_MINUTES' | 'EIP_MANAGEMENT' | 'EIG_DRAFTING' | 'EIG_APPROVAL' | 'COMPANY_PROFILE_MANAGEMENT' | 'COMPANY_PROFILE_NEWS' | 'COMPANY_PROFILE_DOCUMENTS' | 'BOARD_MEETING_MANAGEMENT' | 'BOARD_MEETING_DRAFTING' | 'BOARD_MEETING_MINUTES' | 'BOARD_SETUP' | 'COMMITTEE_MANAGEMENT' | 'COMMITTEE_MEETINGS_DRAFTING' | 'COMMITTEE_MEETINGS_MINUTES' | 'COMMITTEE_MEETINGS_MANAGEMENT' | 'PERMISSIONS' | 'DELEGATIONS' | 'SUBSCRIPTION' | 'CAP_TABLE_VIEW' | 'CAP_TABLE_SETUP' | 'CAP_TABLE_ISSUE_BONUS_SHARES' | 'CAP_TABLE_ISSUE_SHARES' | 'CAP_TABLE_CHANGE_PAR_VALUE' | 'CAP_TABLE_UPDATE_SHAREHOLDER' | 'CAP_TABLE_TRADING' | 'CAP_TABLE_NOTES' | 'CAP_TABLE_SHARE_CLASSES' | 'ASSEMBLY_VOTE'; };
    'PermissionAssignment': { kind: 'OBJECT'; name: 'PermissionAssignment'; fields: { 'all': { name: 'all'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionOption'; ofType: null; }; }; } }; 'assembly': { name: 'assembly'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionOption'; ofType: null; }; }; } }; 'boardMeeting': { name: 'boardMeeting'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionOption'; ofType: null; }; }; } }; 'committees': { name: 'committees'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionOption'; ofType: null; }; }; } }; 'esop': { name: 'esop'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionOption'; ofType: null; }; }; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'isEbanaRepresentative': { name: 'isEbanaRepresentative'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'privileges': { name: 'privileges'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionOption'; ofType: null; }; }; } }; 'profile': { name: 'profile'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionOption'; ofType: null; }; }; } }; 'relation': { name: 'relation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'PermissionOption': { kind: 'OBJECT'; name: 'PermissionOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'Permission'; ofType: null; } }; }; };
    'PersonalInformation': { kind: 'OBJECT'; name: 'PersonalInformation'; fields: { 'iban': { name: 'iban'; type: { kind: 'OBJECT'; name: 'Iban'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'NaturalPersonIdentifier'; ofType: null; } }; }; };
    'PersonalInformationPayload': { kind: 'OBJECT'; name: 'PersonalInformationPayload'; fields: { 'personalInformation': { name: 'personalInformation'; type: { kind: 'OBJECT'; name: 'PersonalInformation'; ofType: null; } }; }; };
    'PersonalInformationUpdateForm': { kind: 'OBJECT'; name: 'PersonalInformationUpdateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'bank': { name: 'bank'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'broker': { name: 'broker'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'email': { name: 'email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'iban': { name: 'iban'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'occupation': { name: 'occupation'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'residence': { name: 'residence'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'signature': { name: 'signature'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'tadawulPortfolioId': { name: 'tadawulPortfolioId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'PersonalInformationUpdateInput': { kind: 'INPUT_OBJECT'; name: 'PersonalInformationUpdateInput'; isOneOf: false; inputFields: [{ name: 'iban'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'residence'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'bank'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'occupation'; type: { kind: 'ENUM'; name: 'UserOccupation'; ofType: null; }; defaultValue: null }, { name: 'tadawulPortfolioId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'broker'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'signature'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }]; };
    'PhantomInput': { kind: 'INPUT_OBJECT'; name: 'PhantomInput'; isOneOf: false; inputFields: [{ name: '_'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'PhantomScheme': { kind: 'OBJECT'; name: 'PhantomScheme'; fields: { '_': { name: '_'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; }; };
    'PhoneNumber': { kind: 'OBJECT'; name: 'PhoneNumber'; fields: { 'country': { name: 'country'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'formatted': { name: 'formatted'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'international': { name: 'international'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'national': { name: 'national'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'PressRelease': { kind: 'OBJECT'; name: 'PressRelease'; fields: { 'author': { name: 'author'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'content': { name: 'content'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'coverImage': { name: 'coverImage'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'createdDate': { name: 'createdDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'publishDate': { name: 'publishDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'shortId': { name: 'shortId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'PressReleaseCreateForm': { kind: 'OBJECT'; name: 'PressReleaseCreateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'author': { name: 'author'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'content': { name: 'content'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'covertImage': { name: 'covertImage'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'PressReleaseCreateInput': { kind: 'INPUT_OBJECT'; name: 'PressReleaseCreateInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'content'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'author'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'covertImage'; type: { kind: 'SCALAR'; name: 'Upload'; ofType: null; }; defaultValue: null }]; };
    'PressReleaseManagement': { kind: 'OBJECT'; name: 'PressReleaseManagement'; fields: { 'author': { name: 'author'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'content': { name: 'content'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'coverImage': { name: 'coverImage'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'createdDate': { name: 'createdDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'publishDate': { name: 'publishDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'shortId': { name: 'shortId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'PressReleasePaginated': { kind: 'OBJECT'; name: 'PressReleasePaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PressRelease'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'PressReleasePayload': { kind: 'OBJECT'; name: 'PressReleasePayload'; fields: { 'pressRelease': { name: 'pressRelease'; type: { kind: 'OBJECT'; name: 'PressRelease'; ofType: null; } }; }; };
    'PressReleaseSort': { kind: 'INPUT_OBJECT'; name: 'PressReleaseSort'; isOneOf: false; inputFields: [{ name: 'createdDate'; type: { kind: 'ENUM'; name: 'SortDirection'; ofType: null; }; defaultValue: null }]; };
    'Query': { kind: 'OBJECT'; name: 'Query'; fields: { 'as': { name: 'as'; type: { kind: 'OBJECT'; name: 'As'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'CompanyManagement'; ofType: null; } }; 'fakeMutationForm': { name: 'fakeMutationForm'; type: { kind: 'OBJECT'; name: 'FakeMutationForm'; ofType: null; } }; 'fakeMutationForm_comboboxObject': { name: 'fakeMutationForm_comboboxObject'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'fakeMutationForm_country': { name: 'fakeMutationForm_country'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; } }; 'fakeMutationForm_infiniteNumbers': { name: 'fakeMutationForm_infiniteNumbers'; type: { kind: 'OBJECT'; name: 'FakeMutationNumbersConnection'; ofType: null; } }; 'fakeMutationForm_infiniteNumbers_new': { name: 'fakeMutationForm_infiniteNumbers_new'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'gics': { name: 'gics'; type: { kind: 'OBJECT'; name: 'GICS'; ofType: null; } }; 'me': { name: 'me'; type: { kind: 'OBJECT'; name: 'Me'; ofType: null; } }; 'selectOptions': { name: 'selectOptions'; type: { kind: 'OBJECT'; name: 'SelectOptions'; ofType: null; } }; }; };
    'RSU': { kind: 'OBJECT'; name: 'RSU'; fields: { '_': { name: '_'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; }; };
    'Referral': { kind: 'OBJECT'; name: 'Referral'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'reason': { name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'RegisterAddCommonForm': { kind: 'OBJECT'; name: 'RegisterAddCommonForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardAuthorizationDate': { name: 'boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'initialOfferingPrice': { name: 'initialOfferingPrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'parValue': { name: 'parValue'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'prefix': { name: 'prefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterAddCommonInput': { kind: 'INPUT_OBJECT'; name: 'RegisterAddCommonInput'; isOneOf: false; inputFields: [{ name: 'prefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'parValue'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'initialOfferingPrice'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'boardAuthorizationDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'RegisterAddPreferredForm': { kind: 'OBJECT'; name: 'RegisterAddPreferredForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardAuthorizationDate': { name: 'boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'initialOfferingPrice': { name: 'initialOfferingPrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'liquidationMultiple': { name: 'liquidationMultiple'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'parValue': { name: 'parValue'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'participationMultiple': { name: 'participationMultiple'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'prefix': { name: 'prefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'seniority': { name: 'seniority'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'votesPerShare': { name: 'votesPerShare'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; }; };
    'RegisterAddPreferredInput': { kind: 'INPUT_OBJECT'; name: 'RegisterAddPreferredInput'; isOneOf: false; inputFields: [{ name: 'prefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'parValue'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'initialOfferingPrice'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'boardAuthorizationDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'votesPerShare'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'seniority'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'liquidationMultiple'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'participationMultiple'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'RegisterAddRedeemableForm': { kind: 'OBJECT'; name: 'RegisterAddRedeemableForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardAuthorizationDate': { name: 'boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'parValue': { name: 'parValue'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'prefix': { name: 'prefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'redeemableAfter': { name: 'redeemableAfter'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'redeemableBefore': { name: 'redeemableBefore'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'redemptionPrice': { name: 'redemptionPrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'votesPerShare': { name: 'votesPerShare'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; }; };
    'RegisterAddRedeemableInput': { kind: 'INPUT_OBJECT'; name: 'RegisterAddRedeemableInput'; isOneOf: false; inputFields: [{ name: 'prefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'parValue'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'boardAuthorizationDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'votesPerShare'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'redemptionPrice'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'redeemableAfter'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'redeemableBefore'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'RegisterBatchMoveStockForm': { kind: 'OBJECT'; name: 'RegisterBatchMoveStockForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'document': { name: 'document'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'moves': { name: 'moves'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'moves_from': { name: 'moves_from'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'moves_pricePerShare': { name: 'moves_pricePerShare'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'moves_shareClass': { name: 'moves_shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxShareClass'; ofType: null; } }; 'moves_shareCount': { name: 'moves_shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'moves_toExisting': { name: 'moves_toExisting'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'moves_toId': { name: 'moves_toId'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'moves_toId_type': { name: 'moves_toId_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'moves_toId_value': { name: 'moves_toId_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'moves_toName': { name: 'moves_toName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'moves_toNationality': { name: 'moves_toNationality'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'note': { name: 'note'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterBatchMoveStockInput': { kind: 'INPUT_OBJECT'; name: 'RegisterBatchMoveStockInput'; isOneOf: false; inputFields: [{ name: 'moves'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RegisterBatchMoveStockMoveInput'; ofType: null; }; }; defaultValue: null }, { name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'document'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }]; };
    'RegisterBatchMoveStockMoveInput': { kind: 'INPUT_OBJECT'; name: 'RegisterBatchMoveStockMoveInput'; isOneOf: false; inputFields: [{ name: 'from'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'toId'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'toName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'toNationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'pricePerShare'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'toExisting'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'RegisterBatchMoveStockMovePayload': { kind: 'OBJECT'; name: 'RegisterBatchMoveStockMovePayload'; fields: { 'register': { name: 'register'; type: { kind: 'OBJECT'; name: 'ShareholderRegister'; ofType: null; } }; 'step10': { name: 'step10'; type: { kind: 'OBJECT'; name: 'RegisterBatchMoveStockMoveStep10'; ofType: null; } }; }; };
    'RegisterBatchMoveStockMoveStep10': { kind: 'OBJECT'; name: 'RegisterBatchMoveStockMoveStep10'; fields: { 'from': { name: 'from'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholder'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'toExisting': { name: 'toExisting'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholder'; ofType: null; } }; 'toNewId': { name: 'toNewId'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'toNewName': { name: 'toNewName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'RegisterChangeParValueForm': { kind: 'OBJECT'; name: 'RegisterChangeParValueForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'dueDate': { name: 'dueDate'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'parValue': { name: 'parValue'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterChangeParValueInput': { kind: 'INPUT_OBJECT'; name: 'RegisterChangeParValueInput'; isOneOf: false; inputFields: [{ name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'dueDate'; type: { kind: 'SCALAR'; name: 'LocalDateTime'; ofType: null; }; defaultValue: null }, { name: 'parValue'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'RegisterChangeParValuePreview': { kind: 'OBJECT'; name: 'RegisterChangeParValuePreview'; fields: { 'currentIssuedShares': { name: 'currentIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'currentParValue': { name: 'currentParValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'issuedAmount': { name: 'issuedAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'newIssuedShares': { name: 'newIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'newParValue': { name: 'newParValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'optimizedParValue': { name: 'optimizedParValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'shareholders': { name: 'shareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterPreviewShareholder'; ofType: null; }; } }; }; };
    'RegisterCompanyPayload': { kind: 'OBJECT'; name: 'RegisterCompanyPayload'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'CompanyManagement'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'form': { name: 'form'; type: { kind: 'OBJECT'; name: 'CompanyRegistrationForm'; ofType: null; } }; }; };
    'RegisterIssueBonusSharesForm': { kind: 'OBJECT'; name: 'RegisterIssueBonusSharesForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'bonusShares': { name: 'bonusShares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'dueDate': { name: 'dueDate'; type: { kind: 'OBJECT'; name: 'DateTimeFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'leftoverAssignment': { name: 'leftoverAssignment'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'leftoverAssignment_id': { name: 'leftoverAssignment_id'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'leftoverAssignment_shares': { name: 'leftoverAssignment_shares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterIssueBonusSharesInput': { kind: 'INPUT_OBJECT'; name: 'RegisterIssueBonusSharesInput'; isOneOf: false; inputFields: [{ name: 'bonusShares'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'dueDate'; type: { kind: 'SCALAR'; name: 'LocalDateTime'; ofType: null; }; defaultValue: null }, { name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'leftoverAssignment'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RegisterSharesAssignment'; ofType: null; }; }; defaultValue: null }]; };
    'RegisterIssueBonusSharesPreview': { kind: 'OBJECT'; name: 'RegisterIssueBonusSharesPreview'; fields: { 'currentIssuedAmount': { name: 'currentIssuedAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'currentIssuedShares': { name: 'currentIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'leftoverShares': { name: 'leftoverShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'newIssuedAmount': { name: 'newIssuedAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'newIssuedShares': { name: 'newIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'parValue': { name: 'parValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'shareholders': { name: 'shareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterPreviewShareholder'; ofType: null; }; } }; }; };
    'RegisterIssueSharesAssignmentInput': { kind: 'INPUT_OBJECT'; name: 'RegisterIssueSharesAssignmentInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'nationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shares'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'RegisterIssueSharesForm': { kind: 'OBJECT'; name: 'RegisterIssueSharesForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'assignments': { name: 'assignments'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'assignments_id': { name: 'assignments_id'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'assignments_id_type': { name: 'assignments_id_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'assignments_id_value': { name: 'assignments_id_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'assignments_name': { name: 'assignments_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'assignments_nationality': { name: 'assignments_nationality'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'assignments_shares': { name: 'assignments_shares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'treasuryShares': { name: 'treasuryShares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterIssueSharesInput': { kind: 'INPUT_OBJECT'; name: 'RegisterIssueSharesInput'; isOneOf: false; inputFields: [{ name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'treasuryShares'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'assignments'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RegisterIssueSharesAssignmentInput'; ofType: null; }; }; defaultValue: null }]; };
    'RegisterIssueSharesPreview': { kind: 'OBJECT'; name: 'RegisterIssueSharesPreview'; fields: { 'changedShareholders': { name: 'changedShareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterPreviewShareholder'; ofType: null; }; } }; 'currentIssuedAmount': { name: 'currentIssuedAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'currentIssuedShares': { name: 'currentIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'newIssuedAmount': { name: 'newIssuedAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'newIssuedShares': { name: 'newIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'newShareholders': { name: 'newShareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterPreviewShareholder'; ofType: null; }; } }; 'shareholders': { name: 'shareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterPreviewShareholder'; ofType: null; }; } }; }; };
    'RegisterLockTradingInput': { kind: 'OBJECT'; name: 'RegisterLockTradingInput'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterMoveStockForm': { kind: 'OBJECT'; name: 'RegisterMoveStockForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'document': { name: 'document'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'from': { name: 'from'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'from_type': { name: 'from_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'from_value': { name: 'from_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'note': { name: 'note'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'toId': { name: 'toId'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'toId_type': { name: 'toId_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'toId_value': { name: 'toId_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'toName': { name: 'toName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'toNationality': { name: 'toNationality'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterMoveStockInput': { kind: 'INPUT_OBJECT'; name: 'RegisterMoveStockInput'; isOneOf: false; inputFields: [{ name: 'from'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'toId'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'toName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'toNationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'document'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }]; };
    'RegisterRemoveShareClassForm': { kind: 'OBJECT'; name: 'RegisterRemoveShareClassForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'prefix': { name: 'prefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterRemoveShareClassInput': { kind: 'INPUT_OBJECT'; name: 'RegisterRemoveShareClassInput'; isOneOf: false; inputFields: [{ name: 'prefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'RegisterSharesAssignment': { kind: 'INPUT_OBJECT'; name: 'RegisterSharesAssignment'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shares'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'RegisterUnlockTradingInput': { kind: 'OBJECT'; name: 'RegisterUnlockTradingInput'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterUpdateShareholderForm': { kind: 'OBJECT'; name: 'RegisterUpdateShareholderForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'beneficialOwner': { name: 'beneficialOwner'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'broker': { name: 'broker'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'countryOfResidence': { name: 'countryOfResidence'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'dateOfBirth': { name: 'dateOfBirth'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'edaaPortfolioBroker': { name: 'edaaPortfolioBroker'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'edaaPortfolioNumber': { name: 'edaaPortfolioNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'email': { name: 'email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'founder': { name: 'founder'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'iban': { name: 'iban'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'mobile': { name: 'mobile'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'nameAr': { name: 'nameAr'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'nameEn': { name: 'nameEn'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'occupation': { name: 'occupation'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'restrictions': { name: 'restrictions'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'restrictions_kind': { name: 'restrictions_kind'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'restrictions_note': { name: 'restrictions_note'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'restrictions_shareClass': { name: 'restrictions_shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxShareClass'; ofType: null; } }; 'restrictions_shareCount': { name: 'restrictions_shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'shareholderId': { name: 'shareholderId'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'shareholderId_type': { name: 'shareholderId_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'shareholderId_value': { name: 'shareholderId_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'RegisterUpdateShareholderInput': { kind: 'INPUT_OBJECT'; name: 'RegisterUpdateShareholderInput'; isOneOf: false; inputFields: [{ name: 'shareholderId'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'nameEn'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'nameAr'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'nationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'mobile'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'countryOfResidence'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'dateOfBirth'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'beneficialOwner'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'iban'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'edaaPortfolioNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'edaaPortfolioBroker'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'broker'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'occupation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'founder'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'restrictions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RegisterUpdateShareholderRestrictionInput'; ofType: null; }; }; defaultValue: null }]; };
    'RegisterUpdateShareholderRestrictionInput': { kind: 'INPUT_OBJECT'; name: 'RegisterUpdateShareholderRestrictionInput'; isOneOf: false; inputFields: [{ name: 'kind'; type: { kind: 'ENUM'; name: 'RestrictionKind'; ofType: null; }; defaultValue: null }, { name: 'shareCount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'RestrictionKind': { name: 'RestrictionKind'; enumValues: 'UNSPECIFIED' | 'LOCKUP_PERIOD' | 'JUDICIAL_HOLDING' | 'PLEDGE_TRANSACTION' | 'MINOR' | 'DISEASED'; };
    'RestrictionKindOption': { kind: 'OBJECT'; name: 'RestrictionKindOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'RestrictionKind'; ofType: null; } }; }; };
    'RevokePermissionForm': { kind: 'OBJECT'; name: 'RevokePermissionForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'userId': { name: 'userId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'RevokePermissionInput': { kind: 'INPUT_OBJECT'; name: 'RevokePermissionInput'; isOneOf: false; inputFields: [{ name: 'userId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'SARs': { kind: 'OBJECT'; name: 'SARs'; fields: { 'exercisePrice': { name: 'exercisePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; }; };
    'SARsInput': { kind: 'INPUT_OBJECT'; name: 'SARsInput'; isOneOf: false; inputFields: [{ name: 'currentPrice'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'Safe': { kind: 'OBJECT'; name: 'Safe'; fields: { 'boardAuthorizationDate': { name: 'boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'conversionEvent': { name: 'conversionEvent'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'discount': { name: 'discount'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'file': { name: 'file'; type: { kind: 'INTERFACE'; name: 'FileRef'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'investmentAmount': { name: 'investmentAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'issueDate': { name: 'issueDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'mostFavoredNotation': { name: 'mostFavoredNotation'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'notes': { name: 'notes'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'prorataRight': { name: 'prorataRight'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'shareholder': { name: 'shareholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'ENUM'; name: 'SafeType'; ofType: null; } }; 'valuationCap': { name: 'valuationCap'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; }; };
    'SafeCreateForm': { kind: 'OBJECT'; name: 'SafeCreateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'boardAuthorizationDate': { name: 'boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'companyId': { name: 'companyId'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'conversionEvent': { name: 'conversionEvent'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'discount': { name: 'discount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'file': { name: 'file'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'investmentAmount': { name: 'investmentAmount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'issueDate': { name: 'issueDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'mostFavoredNotation': { name: 'mostFavoredNotation'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'noteholderId': { name: 'noteholderId'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'noteholderId_type': { name: 'noteholderId_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'noteholderId_value': { name: 'noteholderId_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'noteholderName': { name: 'noteholderName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'noteholderNationality': { name: 'noteholderNationality'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'notes': { name: 'notes'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'prorataRight': { name: 'prorataRight'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ComboBoxStringOption'; ofType: null; } }; 'shareholder': { name: 'shareholder'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'valuationCap': { name: 'valuationCap'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; }; };
    'SafeCreateInput': { kind: 'INPUT_OBJECT'; name: 'SafeCreateInput'; isOneOf: false; inputFields: [{ name: 'companyId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'noteholderId'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'noteholderName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'noteholderNationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareClass'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'ENUM'; name: 'SafeType'; ofType: null; }; defaultValue: null }, { name: 'issueDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'investmentAmount'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'valuationCap'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'discount'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'boardAuthorizationDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'mostFavoredNotation'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'prorataRight'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'conversionEvent'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'notes'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'file'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }]; };
    'SafeFilter': { kind: 'INPUT_OBJECT'; name: 'SafeFilter'; isOneOf: false; inputFields: [{ name: 'shareholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shareholder_in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'shareholder_nin'; type: { kind: 'LIST'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'shareholder_contains'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SafeFilter'; ofType: null; }; }; }; defaultValue: null }, { name: 'or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SafeFilter'; ofType: null; }; }; }; defaultValue: null }, { name: 'not'; type: { kind: 'INPUT_OBJECT'; name: 'SafeFilter'; ofType: null; }; defaultValue: null }]; };
    'SafePaginated': { kind: 'OBJECT'; name: 'SafePaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Safe'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'SafePayload': { kind: 'OBJECT'; name: 'SafePayload'; fields: { 'safe': { name: 'safe'; type: { kind: 'OBJECT'; name: 'Safe'; ofType: null; } }; }; };
    'SafeSort': { kind: 'INPUT_OBJECT'; name: 'SafeSort'; isOneOf: false; inputFields: [{ name: 'type'; type: { kind: 'ENUM'; name: 'SortDirection'; ofType: null; }; defaultValue: null }]; };
    'SafeType': { name: 'SafeType'; enumValues: 'PRE_MONEY' | 'POST_MONEY'; };
    'SafeTypeOption': { kind: 'OBJECT'; name: 'SafeTypeOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'SafeType'; ofType: null; } }; }; };
    'SelectFormField': { kind: 'OBJECT'; name: 'SelectFormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'multiselect': { name: 'multiselect'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'SelectOptions': { kind: 'OBJECT'; name: 'SelectOptions'; fields: { 'country': { name: 'country'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; }; } }; 'sector': { name: 'sector'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringOption'; ofType: null; }; }; } }; }; };
    'SetupCapTableForm': { kind: 'OBJECT'; name: 'SetupCapTableForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'authorizedCapital': { name: 'authorizedCapital'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'commonClasses': { name: 'commonClasses'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'commonClasses_boardAuthorizationDate': { name: 'commonClasses_boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'commonClasses_initialOfferingPrice': { name: 'commonClasses_initialOfferingPrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'commonClasses_name': { name: 'commonClasses_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'commonClasses_parValue': { name: 'commonClasses_parValue'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'commonClasses_prefix': { name: 'commonClasses_prefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'commonClasses_shareCount': { name: 'commonClasses_shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'preferredClasses': { name: 'preferredClasses'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'preferredClasses_boardAuthorizationDate': { name: 'preferredClasses_boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'preferredClasses_initialOfferingPrice': { name: 'preferredClasses_initialOfferingPrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'preferredClasses_liquidationMultiple': { name: 'preferredClasses_liquidationMultiple'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'preferredClasses_name': { name: 'preferredClasses_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'preferredClasses_parValue': { name: 'preferredClasses_parValue'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'preferredClasses_participationMultiple': { name: 'preferredClasses_participationMultiple'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'preferredClasses_prefix': { name: 'preferredClasses_prefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'preferredClasses_seniority': { name: 'preferredClasses_seniority'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'preferredClasses_shareCount': { name: 'preferredClasses_shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'preferredClasses_votesPerShare': { name: 'preferredClasses_votesPerShare'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'redeemableClasses': { name: 'redeemableClasses'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'redeemableClasses_boardAuthorizationDate': { name: 'redeemableClasses_boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'redeemableClasses_name': { name: 'redeemableClasses_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'redeemableClasses_parValue': { name: 'redeemableClasses_parValue'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'redeemableClasses_prefix': { name: 'redeemableClasses_prefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'redeemableClasses_redeemableAfter': { name: 'redeemableClasses_redeemableAfter'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'redeemableClasses_redeemableBefore': { name: 'redeemableClasses_redeemableBefore'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'redeemableClasses_redemptionPrice': { name: 'redeemableClasses_redemptionPrice'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'redeemableClasses_shareCount': { name: 'redeemableClasses_shareCount'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'redeemableClasses_votesPerShare': { name: 'redeemableClasses_votesPerShare'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'shareholders': { name: 'shareholders'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'shareholders_countryOfResidence': { name: 'shareholders_countryOfResidence'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'shareholders_dateOfBirth': { name: 'shareholders_dateOfBirth'; type: { kind: 'OBJECT'; name: 'DateFormField'; ofType: null; } }; 'shareholders_email': { name: 'shareholders_email'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareholders_id': { name: 'shareholders_id'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'shareholders_id_type': { name: 'shareholders_id_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'shareholders_id_value': { name: 'shareholders_id_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareholders_mobileNumber': { name: 'shareholders_mobileNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareholders_name': { name: 'shareholders_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareholders_nationality': { name: 'shareholders_nationality'; type: { kind: 'OBJECT'; name: 'ComboBoxCountry'; ofType: null; } }; 'shareholders_occupation': { name: 'shareholders_occupation'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareholders_ownership': { name: 'shareholders_ownership'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'shareholders_ownership_count': { name: 'shareholders_ownership_count'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'shareholders_ownership_prefix': { name: 'shareholders_ownership_prefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'shareholders_ownership_shareClassName': { name: 'shareholders_ownership_shareClassName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'treasury': { name: 'treasury'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'treasury_shareClassPrefix': { name: 'treasury_shareClassPrefix'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'treasury_shares': { name: 'treasury_shares'; type: { kind: 'OBJECT'; name: 'NumberFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'SetupCapTableInput': { kind: 'INPUT_OBJECT'; name: 'SetupCapTableInput'; isOneOf: false; inputFields: [{ name: 'authorizedCapital'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'commonClasses'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RegisterAddCommonInput'; ofType: null; }; }; defaultValue: null }, { name: 'preferredClasses'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RegisterAddPreferredInput'; ofType: null; }; }; defaultValue: null }, { name: 'redeemableClasses'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RegisterAddRedeemableInput'; ofType: null; }; }; defaultValue: null }, { name: 'treasury'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SetupCapTableTreasuryInput'; ofType: null; }; }; defaultValue: null }, { name: 'shareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SetupCapTableShareholderInput'; ofType: null; }; }; defaultValue: null }]; };
    'SetupCapTablePayload': { kind: 'OBJECT'; name: 'SetupCapTablePayload'; fields: { 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'form': { name: 'form'; type: { kind: 'OBJECT'; name: 'SetupCapTableForm'; ofType: null; } }; 'register': { name: 'register'; type: { kind: 'OBJECT'; name: 'ShareholderRegister'; ofType: null; } }; }; };
    'SetupCapTableShareholderInput': { kind: 'INPUT_OBJECT'; name: 'SetupCapTableShareholderInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'nationality'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'ownership'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'OwnershipInput'; ofType: null; }; }; defaultValue: null }, { name: 'mobileNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'countryOfResidence'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'dateOfBirth'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; }; defaultValue: null }, { name: 'occupation'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'SetupCapTableTreasuryInput': { kind: 'INPUT_OBJECT'; name: 'SetupCapTableTreasuryInput'; isOneOf: false; inputFields: [{ name: 'shareClassPrefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'shares'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'ShareClass': { kind: 'OBJECT'; name: 'ShareClass'; fields: { 'boardAuthorizationDate': { name: 'boardAuthorizationDate'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'initialOfferingPrice': { name: 'initialOfferingPrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'issuedAmount': { name: 'issuedAmount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'issuedShares': { name: 'issuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'liquidationPreference': { name: 'liquidationPreference'; type: { kind: 'OBJECT'; name: 'LiquidationPreference'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'parValue': { name: 'parValue'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'prefix': { name: 'prefix'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'redeemableAfter': { name: 'redeemableAfter'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'redeemableBefore': { name: 'redeemableBefore'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'redemptionPrice': { name: 'redemptionPrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'removeForm': { name: 'removeForm'; type: { kind: 'OBJECT'; name: 'RegisterRemoveShareClassForm'; ofType: null; } }; 'treasuryShares': { name: 'treasuryShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'votesPerShare': { name: 'votesPerShare'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'ShareDistributionSummary': { kind: 'OBJECT'; name: 'ShareDistributionSummary'; fields: { 'available': { name: 'available'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'exercised': { name: 'exercised'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'issued': { name: 'issued'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'settled': { name: 'settled'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vested': { name: 'vested'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'vesting': { name: 'vesting'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; }; };
    'Shareholder': { kind: 'OBJECT'; name: 'Shareholder'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'ShareholderRegister': { kind: 'OBJECT'; name: 'ShareholderRegister'; fields: { 'addCommonForm': { name: 'addCommonForm'; type: { kind: 'OBJECT'; name: 'RegisterAddCommonForm'; ofType: null; } }; 'addPreferredForm': { name: 'addPreferredForm'; type: { kind: 'OBJECT'; name: 'RegisterAddPreferredForm'; ofType: null; } }; 'addRedeemableForm': { name: 'addRedeemableForm'; type: { kind: 'OBJECT'; name: 'RegisterAddRedeemableForm'; ofType: null; } }; 'batchMoveStockForm': { name: 'batchMoveStockForm'; type: { kind: 'OBJECT'; name: 'RegisterBatchMoveStockForm'; ofType: null; } }; 'capital': { name: 'capital'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterCapital'; ofType: null; } }; 'changeParValueForm': { name: 'changeParValueForm'; type: { kind: 'OBJECT'; name: 'RegisterChangeParValueForm'; ofType: null; } }; 'estimatedMarketCap': { name: 'estimatedMarketCap'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'events': { name: 'events'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterEvent'; ofType: null; }; } }; 'fitUnfit': { name: 'fitUnfit'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterFitUnfit'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'issueBonusSharesForm': { name: 'issueBonusSharesForm'; type: { kind: 'OBJECT'; name: 'RegisterIssueBonusSharesForm'; ofType: null; } }; 'issueSharesForm': { name: 'issueSharesForm'; type: { kind: 'OBJECT'; name: 'RegisterIssueSharesForm'; ofType: null; } }; 'lastUpdate': { name: 'lastUpdate'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterEvent'; ofType: null; } }; 'lockTradingForm': { name: 'lockTradingForm'; type: { kind: 'OBJECT'; name: 'RegisterLockTradingInput'; ofType: null; } }; 'moveStockForm': { name: 'moveStockForm'; type: { kind: 'OBJECT'; name: 'RegisterMoveStockForm'; ofType: null; } }; 'movesList': { name: 'movesList'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StockMoves'; ofType: null; }; } }; 'shareholder': { name: 'shareholder'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholder'; ofType: null; } }; 'shareholder_options': { name: 'shareholder_options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholder'; ofType: null; }; } }; 'shareholdersPaginated': { name: 'shareholdersPaginated'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholderPaginated'; ofType: null; } }; 'ticker': { name: 'ticker'; type: { kind: 'OBJECT'; name: 'StockTicker'; ofType: null; } }; 'top5': { name: 'top5'; type: { kind: 'OBJECT'; name: 'ShareholderRegisterTop5'; ofType: null; } }; 'totalShareholders': { name: 'totalShareholders'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'tradingLocked': { name: 'tradingLocked'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'treasuryShares': { name: 'treasuryShares'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterTreasuryShares'; ofType: null; }; } }; 'unlockTradingForm': { name: 'unlockTradingForm'; type: { kind: 'OBJECT'; name: 'RegisterUnlockTradingInput'; ofType: null; } }; 'updateShareholdersFitStatusForm': { name: 'updateShareholdersFitStatusForm'; type: { kind: 'OBJECT'; name: 'UpdateShareholdersFitStatusForm'; ofType: null; } }; 'xlsx': { name: 'xlsx'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; }; };
    'ShareholderRegisterCapital': { kind: 'OBJECT'; name: 'ShareholderRegisterCapital'; fields: { 'authorizedAmount': { name: 'authorizedAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'commonClasses': { name: 'commonClasses'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; }; } }; 'commonIssuedAmount': { name: 'commonIssuedAmount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'commonIssuedShares': { name: 'commonIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'issuedAmount': { name: 'issuedAmount'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'issuedShares': { name: 'issuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'preferredClasses': { name: 'preferredClasses'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; }; } }; 'preferredIssuedAmount': { name: 'preferredIssuedAmount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'preferredIssuedShares': { name: 'preferredIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'redeemableClasses': { name: 'redeemableClasses'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; }; } }; 'redeemableIssuedAmount': { name: 'redeemableIssuedAmount'; type: { kind: 'OBJECT'; name: 'NumberToTotal'; ofType: null; } }; 'redeemableIssuedShares': { name: 'redeemableIssuedShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'shareClasses': { name: 'shareClasses'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; }; } }; }; };
    'ShareholderRegisterEvent': { kind: 'OBJECT'; name: 'ShareholderRegisterEvent'; fields: { 'description': { name: 'description'; type: { kind: 'OBJECT'; name: 'Text'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'recordedAt': { name: 'recordedAt'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'userId': { name: 'userId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'userName': { name: 'userName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'ShareholderRegisterFitUnfit': { kind: 'OBJECT'; name: 'ShareholderRegisterFitUnfit'; fields: { 'customErrorMessage': { name: 'customErrorMessage'; type: { kind: 'OBJECT'; name: 'CustomErrorMessage'; ofType: null; } }; 'enabled': { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'policy': { name: 'policy'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; }; };
    'ShareholderRegisterOwnership': { kind: 'OBJECT'; name: 'ShareholderRegisterOwnership'; fields: { 'cost': { name: 'cost'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'ShareholderRegisterPayload': { kind: 'OBJECT'; name: 'ShareholderRegisterPayload'; fields: { 'register': { name: 'register'; type: { kind: 'OBJECT'; name: 'ShareholderRegister'; ofType: null; } }; }; };
    'ShareholderRegisterPreviewShareholder': { kind: 'OBJECT'; name: 'ShareholderRegisterPreviewShareholder'; fields: { 'afterShares': { name: 'afterShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'beforeShares': { name: 'beforeShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'ShareholderRegisterRestriction': { kind: 'OBJECT'; name: 'ShareholderRegisterRestriction'; fields: { 'kind': { name: 'kind'; type: { kind: 'OBJECT'; name: 'RestrictionKindOption'; ofType: null; } }; 'note': { name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'ShareholderRegisterShareholder': { kind: 'OBJECT'; name: 'ShareholderRegisterShareholder'; fields: { 'beneficialOwnerName': { name: 'beneficialOwnerName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'certificate': { name: 'certificate'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'commonShares': { name: 'commonShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'cost': { name: 'cost'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'countryOfResidence': { name: 'countryOfResidence'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'dateOfBirth': { name: 'dateOfBirth'; type: { kind: 'OBJECT'; name: 'LocalDateElements'; ofType: null; } }; 'edaaPortfolio': { name: 'edaaPortfolio'; type: { kind: 'OBJECT'; name: 'EdaaPortfolio'; ofType: null; } }; 'email': { name: 'email'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'fitUnfit': { name: 'fitUnfit'; type: { kind: 'OBJECT'; name: 'FitUnfit'; ofType: null; } }; 'founder': { name: 'founder'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'iban': { name: 'iban'; type: { kind: 'OBJECT'; name: 'Iban'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'inputIdType': { name: 'inputIdType'; type: { kind: 'ENUM'; name: 'LegalPersonIdentifierInputType'; ofType: null; } }; 'mobileNumber': { name: 'mobileNumber'; type: { kind: 'OBJECT'; name: 'PhoneNumber'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'nationality': { name: 'nationality'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'ownership': { name: 'ownership'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterOwnership'; ofType: null; }; } }; 'preferredShares': { name: 'preferredShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'redeemableShares': { name: 'redeemableShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'restrictions': { name: 'restrictions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterRestriction'; ofType: null; }; } }; 'totalOwnership': { name: 'totalOwnership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'totalShares': { name: 'totalShares'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'updateForm': { name: 'updateForm'; type: { kind: 'OBJECT'; name: 'RegisterUpdateShareholderForm'; ofType: null; } }; }; };
    'ShareholderRegisterShareholderPaginated': { kind: 'OBJECT'; name: 'ShareholderRegisterShareholderPaginated'; fields: { 'nodes': { name: 'nodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholder'; ofType: null; }; }; }; } }; 'number': { name: 'number'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalElements': { name: 'totalElements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalPages': { name: 'totalPages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'ShareholderRegisterTop5': { kind: 'OBJECT'; name: 'ShareholderRegisterTop5'; fields: { 'ownership': { name: 'ownership'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'shareholders': { name: 'shareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ShareholderRegisterShareholder'; ofType: null; }; } }; }; };
    'ShareholderRegisterTreasuryShares': { kind: 'OBJECT'; name: 'ShareholderRegisterTreasuryShares'; fields: { 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'ShareholderType': { name: 'ShareholderType'; enumValues: 'PERSON' | 'COMPANY'; };
    'SimpleFileRef': { kind: 'OBJECT'; name: 'SimpleFileRef'; fields: { 'attachmentUrl': { name: 'attachmentUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'contentLength': { name: 'contentLength'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'contentType': { name: 'contentType'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'filename': { name: 'filename'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'form': { name: 'form'; type: { kind: 'OBJECT'; name: 'SimpleForm'; ofType: null; } }; 'inlineUrl': { name: 'inlineUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; 'key': { name: 'key'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'url': { name: 'url'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; } }; }; };
    'SimpleForm': { kind: 'OBJECT'; name: 'SimpleForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'SmartInsight': { kind: 'OBJECT'; name: 'SmartInsight'; fields: { 'dismissForm': { name: 'dismissForm'; type: { kind: 'OBJECT'; name: 'CompanyDismissInsightForm'; ofType: null; } }; 'insight': { name: 'insight'; type: { kind: 'ENUM'; name: 'DashboardInsight'; ofType: null; } }; }; };
    'SocialAccount': { kind: 'OBJECT'; name: 'SocialAccount'; fields: { 'account': { name: 'account'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'network': { name: 'network'; type: { kind: 'ENUM'; name: 'SocialNetwork'; ofType: null; } }; 'url': { name: 'url'; type: { kind: 'OBJECT'; name: 'UriComponents'; ofType: null; } }; }; };
    'SocialAccountInput': { kind: 'INPUT_OBJECT'; name: 'SocialAccountInput'; isOneOf: false; inputFields: [{ name: 'network'; type: { kind: 'ENUM'; name: 'SocialNetwork'; ofType: null; }; defaultValue: null }, { name: 'account'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'SocialNetwork': { name: 'SocialNetwork'; enumValues: 'TWITTER' | 'FACEBOOK' | 'SNAPCHAT' | 'INSTAGRAM' | 'LINKEDIN' | 'YOUTUBE' | 'TIKTOK' | 'TELEGRAM' | 'WHATSAPP'; };
    'SocialNetworkOption': { kind: 'OBJECT'; name: 'SocialNetworkOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'SocialNetwork'; ofType: null; } }; }; };
    'SortDirection': { name: 'SortDirection'; enumValues: 'ASC' | 'DESC'; };
    'StockMove': { kind: 'OBJECT'; name: 'StockMove'; fields: { 'from': { name: 'from'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'pricePerShare': { name: 'pricePerShare'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'shareClass': { name: 'shareClass'; type: { kind: 'OBJECT'; name: 'ShareClass'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'to': { name: 'to'; type: { kind: 'OBJECT'; name: 'LegalPerson'; ofType: null; } }; }; };
    'StockMoves': { kind: 'OBJECT'; name: 'StockMoves'; fields: { 'createdDate': { name: 'createdDate'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'document': { name: 'document'; type: { kind: 'OBJECT'; name: 'SimpleFileRef'; ofType: null; } }; 'moves': { name: 'moves'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StockMove'; ofType: null; }; } }; 'note': { name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'StockOptions': { kind: 'OBJECT'; name: 'StockOptions'; fields: { 'exercisePrice': { name: 'exercisePrice'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; }; };
    'StockOptionsGainChartEntry': { kind: 'OBJECT'; name: 'StockOptionsGainChartEntry'; fields: { 'gain': { name: 'gain'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'x': { name: 'x'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'y': { name: 'y'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'StockOptionsInput': { kind: 'INPUT_OBJECT'; name: 'StockOptionsInput'; isOneOf: false; inputFields: [{ name: 'exercisePrice'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'StockTicker': { kind: 'OBJECT'; name: 'StockTicker'; fields: { 'change': { name: 'change'; type: { kind: 'OBJECT'; name: 'Percentage'; ofType: null; } }; 'daily': { name: 'daily'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StockTickerTick'; ofType: null; }; } }; 'last': { name: 'last'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'weekly': { name: 'weekly'; type: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'StockTickerTick'; ofType: null; }; } }; }; };
    'StockTickerTick': { kind: 'OBJECT'; name: 'StockTickerTick'; fields: { 'close': { name: 'close'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'end': { name: 'end'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'high': { name: 'high'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'low': { name: 'low'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'open': { name: 'open'; type: { kind: 'OBJECT'; name: 'Money'; ofType: null; } }; 'start': { name: 'start'; type: { kind: 'OBJECT'; name: 'LocalDateTimeElements'; ofType: null; } }; 'transactions': { name: 'transactions'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; }; };
    'String': unknown;
    'StringOption': { kind: 'OBJECT'; name: 'StringOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'SubscriptionCreateForm': { kind: 'OBJECT'; name: 'SubscriptionCreateForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'plan': { name: 'plan'; type: { kind: 'OBJECT'; name: 'ComboBoxCompanyPlan'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'SubscriptionCreateInput': { kind: 'INPUT_OBJECT'; name: 'SubscriptionCreateInput'; isOneOf: false; inputFields: [{ name: 'plan'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'SubscriptionRenewForm': { kind: 'OBJECT'; name: 'SubscriptionRenewForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'SubscriptionUpgradeForm': { kind: 'OBJECT'; name: 'SubscriptionUpgradeForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'plan': { name: 'plan'; type: { kind: 'OBJECT'; name: 'ComboBoxCompanyPlan'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'SubscriptionUpgradeInput': { kind: 'INPUT_OBJECT'; name: 'SubscriptionUpgradeInput'; isOneOf: false; inputFields: [{ name: 'plan'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'SummaryStatistics': { kind: 'OBJECT'; name: 'SummaryStatistics'; fields: { 'average': { name: 'average'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'max': { name: 'max'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'min': { name: 'min'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'Tag': { kind: 'INPUT_OBJECT'; name: 'Tag'; isOneOf: false; inputFields: [{ name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'TermLength': { name: 'TermLength'; enumValues: 'MONTHLY' | 'QUARTERLY' | 'YEARLY'; };
    'TermLengthOption': { kind: 'OBJECT'; name: 'TermLengthOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'TermLength'; ofType: null; } }; }; };
    'Termination': { kind: 'OBJECT'; name: 'Termination'; fields: { 'id': { name: 'id'; type: { kind: 'OBJECT'; name: 'LegalPersonIdentifier'; ofType: null; } }; 'reason': { name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'Text': { kind: 'OBJECT'; name: 'Text'; fields: { 'escapedHtml': { name: 'escapedHtml'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'markdown': { name: 'markdown'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'raw': { name: 'raw'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'safeHtml': { name: 'safeHtml'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'unsafeHtml': { name: 'unsafeHtml'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'TextFormField': { kind: 'OBJECT'; name: 'TextFormField'; fields: { 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'helperText': { name: 'helperText'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'helperTextCodes': { name: 'helperTextCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'labelCodes': { name: 'labelCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'path': { name: 'path'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'pattern': { name: 'pattern'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'patternMessage': { name: 'patternMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholder': { name: 'placeholder'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'placeholderCodes': { name: 'placeholderCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'required': { name: 'required'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiredMessage': { name: 'requiredMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'ENUM'; name: 'FieldStatus'; ofType: null; } }; 'statusMessage': { name: 'statusMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltip': { name: 'tooltip'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'tooltipCodes': { name: 'tooltipCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; }; };
    'Timeline': { name: 'Timeline'; enumValues: 'FUTURE' | 'PAST'; };
    'Todo': { kind: 'INTERFACE'; name: 'Todo'; fields: { 'company': { name: 'company'; type: { kind: 'OBJECT'; name: 'Company'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; possibleTypes: 'AssemblyMeetingMinutesTodo' | 'AssemblyVoteTodo' | 'BoardMeetingVoteTodo' | 'BoardResolutionVoteTodo' | 'CommitteeMeetingVoteTodo' | 'CompanyRegistrationApprovalTodo' | 'CompanyRegistrationDelegationTodo' | 'DirectTradeAcceptTodo' | 'DirectTradePaymentTodo' | 'EigGranteeApprovalTodo' | 'EigSendOfferTodo' | 'EigSettleTermTodo' | 'GovernanceCircularResolutionApproveTodo' | 'GovernanceMeetingMinutesTodo'; };
    'TypeOfEquity': { kind: 'OBJECT'; name: 'TypeOfEquity'; fields: { 'type': { name: 'type'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; }; };
    'URI': unknown;
    'UnavailabilityCode': { name: 'UnavailabilityCode'; enumValues: 'NO_EIP'; };
    'UpdateAssemblyMinutesInputItem': { kind: 'INPUT_OBJECT'; name: 'UpdateAssemblyMinutesInputItem'; isOneOf: false; inputFields: [{ name: 'summary'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'discussion'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'UpdateCompanyAddressInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCompanyAddressInput'; isOneOf: false; inputFields: [{ name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'district'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'streetName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'zipCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'UpdateCompanyExecutiveInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCompanyExecutiveInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'position'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'bio'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'photo'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }, { name: 'socialAccounts'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SocialAccountInput'; ofType: null; }; }; }; defaultValue: null }]; };
    'UpdateCompanyForm': { kind: 'OBJECT'; name: 'UpdateCompanyForm'; fields: { 'address': { name: 'address'; type: { kind: 'OBJECT'; name: 'FormFieldGroup'; ofType: null; } }; 'address_city': { name: 'address_city'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'address_district': { name: 'address_district'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'address_streetName': { name: 'address_streetName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'address_zipCode': { name: 'address_zipCode'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'bio': { name: 'bio'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'contactEmail': { name: 'contactEmail'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'contactPhoneNumber': { name: 'contactPhoneNumber'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'executives': { name: 'executives'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'executives_bio': { name: 'executives_bio'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'executives_name': { name: 'executives_name'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'executives_photo': { name: 'executives_photo'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'executives_position': { name: 'executives_position'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'executives_socialAccounts': { name: 'executives_socialAccounts'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'executives_socialAccounts_account': { name: 'executives_socialAccounts_account'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'executives_socialAccounts_network': { name: 'executives_socialAccounts_network'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'gicsCode': { name: 'gicsCode'; type: { kind: 'OBJECT'; name: 'BasicComboBoxFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'logo': { name: 'logo'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'showMarketValueChart': { name: 'showMarketValueChart'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'slug': { name: 'slug'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'socialAccounts': { name: 'socialAccounts'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'socialAccounts_account': { name: 'socialAccounts_account'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'socialAccounts_network': { name: 'socialAccounts_network'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'stamp': { name: 'stamp'; type: { kind: 'OBJECT'; name: 'FileFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'tradeName': { name: 'tradeName'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; 'videoUrl': { name: 'videoUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'websiteUrl': { name: 'websiteUrl'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; }; };
    'UpdateCompanyInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCompanyInput'; isOneOf: false; inputFields: [{ name: 'tradeName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'slug'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'websiteUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; }; defaultValue: null }, { name: 'videoUrl'; type: { kind: 'SCALAR'; name: 'URI'; ofType: null; }; defaultValue: null }, { name: 'bio'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'gicsCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'contactEmail'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'contactPhoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'socialAccounts'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SocialAccountInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'address'; type: { kind: 'INPUT_OBJECT'; name: 'UpdateCompanyAddressInput'; ofType: null; }; defaultValue: null }, { name: 'executives'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UpdateCompanyExecutiveInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'showMarketValueChart'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'logo'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }, { name: 'stamp'; type: { kind: 'INPUT_OBJECT'; name: 'UploadInput'; ofType: null; }; defaultValue: null }]; };
    'UpdateShareholdersFitStatusForm': { kind: 'OBJECT'; name: 'UpdateShareholdersFitStatusForm'; fields: { 'as': { name: 'as'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'available': { name: 'available'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'confirmation': { name: 'confirmation'; type: { kind: 'OBJECT'; name: 'FormConfirmation'; ofType: null; } }; 'defaultValues': { name: 'defaultValues'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'fit': { name: 'fit'; type: { kind: 'OBJECT'; name: 'BooleanFormField'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'shareholders': { name: 'shareholders'; type: { kind: 'OBJECT'; name: 'ListFormField'; ofType: null; } }; 'shareholders_type': { name: 'shareholders_type'; type: { kind: 'OBJECT'; name: 'SelectFormField'; ofType: null; } }; 'shareholders_value': { name: 'shareholders_value'; type: { kind: 'OBJECT'; name: 'TextFormField'; ofType: null; } }; 'submission': { name: 'submission'; type: { kind: 'OBJECT'; name: 'FormSubmission'; ofType: null; } }; 'unavailabilityCodes': { name: 'unavailabilityCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'ENUM'; name: 'UnavailabilityCode'; ofType: null; }; } }; }; };
    'UpdateShareholdersFitStatusInput': { kind: 'INPUT_OBJECT'; name: 'UpdateShareholdersFitStatusInput'; isOneOf: false; inputFields: [{ name: 'shareholders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'LegalPersonIdentifierInput'; ofType: null; }; }; defaultValue: null }, { name: 'fit'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'Upload': unknown;
    'UploadInput': { kind: 'INPUT_OBJECT'; name: 'UploadInput'; isOneOf: false; inputFields: [{ name: 'file'; type: { kind: 'SCALAR'; name: 'Upload'; ofType: null; }; defaultValue: null }, { name: 'url'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }, { name: 'key'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'UriComponents': { kind: 'OBJECT'; name: 'UriComponents'; fields: { 'full': { name: 'full'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'scheme': { name: 'scheme'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'short': { name: 'short'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'UserOccupation': { name: 'UserOccupation'; enumValues: 'GOVERNMENTAL' | 'NONGOVERNMENTAL'; };
    'UserOccupationOption': { kind: 'OBJECT'; name: 'UserOccupationOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'UserOccupation'; ofType: null; } }; }; };
    'VestingScheduleTermStatus': { name: 'VestingScheduleTermStatus'; enumValues: 'NOT_VESTED' | 'VESTED' | 'EXPIRED' | 'EXTEND_EXPIRY_DATE_REQUESTED' | 'UNDER_REVIEW' | 'EXERCISE_APPROVED' | 'EXERCISE_REJECTED' | 'SETTLED' | 'GRANT_TERMINATED'; };
    'VestingScheduleTermStatusOption': { kind: 'OBJECT'; name: 'VestingScheduleTermStatusOption'; fields: { 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'value': { name: 'value'; type: { kind: 'ENUM'; name: 'VestingScheduleTermStatus'; ofType: null; } }; }; };
    'VestingTerm': { kind: 'OBJECT'; name: 'VestingTerm'; fields: { 'due': { name: 'due'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'exerciseForm': { name: 'exerciseForm'; type: { kind: 'OBJECT'; name: 'EIGExerciseForm'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'kpi': { name: 'kpi'; type: { kind: 'OBJECT'; name: 'Kpi'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'optionExpiryDate': { name: 'optionExpiryDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'rejectionReason': { name: 'rejectionReason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'start': { name: 'start'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'VestingScheduleTermStatusOption'; ofType: null; } }; }; };
    'VestingTermManagement': { kind: 'OBJECT'; name: 'VestingTermManagement'; fields: { 'approveExerciseForm': { name: 'approveExerciseForm'; type: { kind: 'OBJECT'; name: 'EIGApproveExerciseForm'; ofType: null; } }; 'due': { name: 'due'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'kpi': { name: 'kpi'; type: { kind: 'OBJECT'; name: 'Kpi'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'optionExpiryDate': { name: 'optionExpiryDate'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'rejectExerciseForm': { name: 'rejectExerciseForm'; type: { kind: 'OBJECT'; name: 'EIGRejectExerciseForm'; ofType: null; } }; 'rejectionReason': { name: 'rejectionReason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'settleForm': { name: 'settleForm'; type: { kind: 'OBJECT'; name: 'EIGSettleForm'; ofType: null; } }; 'shareCount': { name: 'shareCount'; type: { kind: 'OBJECT'; name: 'Number'; ofType: null; } }; 'start': { name: 'start'; type: { kind: 'SCALAR'; name: 'LocalDate'; ofType: null; } }; 'status': { name: 'status'; type: { kind: 'OBJECT'; name: 'VestingScheduleTermStatusOption'; ofType: null; } }; }; };
};

/** An IntrospectionQuery representation of your schema.
 *
 * @remarks
 * This is an introspection of your schema saved as a file by GraphQLSP.
 * It will automatically be used by `gql.tada` to infer the types of your GraphQL documents.
 * If you need to reuse this data or update your `scalars`, update `tadaOutputLocation` to
 * instead save to a .ts instead of a .d.ts file.
 */
export type introspection = {
  name: never;
  query: 'Query';
  mutation: 'Mutation';
  subscription: never;
  types: introspection_types;
};

import * as gqlTada from 'gql.tada';

declare module 'gql.tada' {
  interface setupSchema {
    introspection: introspection
  }
}