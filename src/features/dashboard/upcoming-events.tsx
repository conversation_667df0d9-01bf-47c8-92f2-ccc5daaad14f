import { FragmentOf, graphql, readFragment, ResultOf } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Stack } from '@chakra-ui/react'
import NextLink from 'next/link'
import { route } from 'nextjs-routes'

UpcomingEvents.fragment = graphql(`
  fragment UpcomingEvent on GovernanceMeeting {
    id
    groupId
    groupType
    members {
      name
    }
    title
    date {
      relativeShort
      relativeSeconds

      time {
        default
      }
      date {
        default
      }
      month {
        label
      }
      year
      monthOfYear
      dayOfMonth
    }
  }
`)

export default function UpcomingEvents({ data }: { data: FragmentOf<typeof UpcomingEvents.fragment>[] }) {
  const { t } = useEbanaLocale()
  const upcomingMeetings = data != null && data.map((f) => readFragment(UpcomingEvents.fragment, f))

  return (
    <Stack bg='white' borderRadius='14px' px='1rem' py='1.25rem'>
      {upcomingMeetings.length > 0 && (
        <Stack ms='-1rem' width='calc( 100% + 2rem )' mt='1rem' gap={0}>
          <Box ms='1rem' textStyle='body1' fontWeight={600}>
            {t('upcoming_events')} ({upcomingMeetings.length})
          </Box>
          {upcomingMeetings.map((event, index) => (
            <MeetingCard key={event.id} event={event} index={index} total={upcomingMeetings.length} />
          ))}
        </Stack>
      )}
    </Stack>
  )
}

function MeetingFooter({
  members,
  startsIn,
  relativeSeconds,
}: {
  members: ResultOf<typeof UpcomingEvents.fragment>['members'][0][]
  startsIn: string
  relativeSeconds: number
}) {
  const colors = ['#00263A', '#00BFB2', '#5858FF']

  const statusColor = Number(relativeSeconds) < 0 ? '#FF5630' : Number(relativeSeconds) < 86400 ? '#FEC412' : '#6490EB'

  const bgColor = statusColor + '1A'

  return (
    <Flex justifyContent='space-between'>
      <Flex ms='1rem' fontWeight={500} my='auto'>
        {members.slice(0, 3).map((member, i) => (
          <Box
            key={i}
            ms='-1rem'
            borderRadius='50px'
            bgColor={colors[i % colors.length]}
            p='4px'
            alignContent='center'
            aspectRatio={1}
            height='30px'
            textStyle='body4'
            color='white'>
            <Box mx='auto' width='fit-content'>
              {member.name
                .toUpperCase()
                .split(' ')
                .filter((_, i, arr) => i === 0 || i === arr.length - 1)
                .map((n) => n[0])
                .join('.')}
            </Box>
          </Box>
        ))}

        {members.length > 3 && (
          <Box
            ms='-1rem'
            borderRadius='50px'
            bgColor='#003B5C'
            p='4px'
            alignContent='center'
            aspectRatio={1}
            height='30px'
            textStyle='body4'
            color='white'>
            <Box mx='auto' width='fit-content'>
              +{members.length - 3}
            </Box>
          </Box>
        )}
      </Flex>

      <Flex ms='15px' my='auto' gap='5px' bg={bgColor} px='10px' py='3px' borderRadius='50px' height='fit-content'>
        <Box my='auto' bg={statusColor} borderRadius='50px' h='10px' w='10px' />
        <Box textStyle='body4' fontWeight={500} my='auto'>
          {startsIn}
          {/* {Number(relativeSeconds) < 0
            ? t('upcoming')
            : Number(relativeSeconds) < 86400
              ? formatRelativeTime(relativeSeconds)
              : t('started')} */}
        </Box>
      </Flex>
    </Flex>
  )
}

function MeetingCard({
  event,
  index,
  total,
}: {
  event: ResultOf<typeof UpcomingEvents.fragment>
  index: number
  total: number
}) {
  const { lng } = useEbanaLocale()

  return (
    <NextLink
      href={
        event.groupType === 'BOARD_OF_DIRECTORS'
          ? route({
              pathname: '/[lng]/individual/board/[boardId]/meetings/[id]',
              query: { lng, boardId: event.groupId, id: event.id },
            })
          : route({
              pathname: '/[lng]/individual/committees/[committeeId]/meetings/[id]',
              query: { lng, committeeId: event.groupId, id: event.id },
            })
      }>
      <Stack
        p='1rem'
        pb={index === total - 1 ? '3px' : '23px'}
        borderTop={index !== 0 ? '1px' : null}
        borderColor='stroke.100'
        gap='1rem'>
        <Flex gap='0.5rem'>
          <Stack
            minW='56px'
            overflow='hidden'
            gap={0}
            height='56px'
            width='56px'
            borderRadius='8px'
            borderWidth='1px'
            borderColor='#00263A'>
            <Box
              alignContent='center'
              justifyContent='center'
              height='16px'
              width='100%'
              bgColor='#00263A'
              color='white'
              textStyle='tagSm'>
              <Box mx='auto' w='fit-content'>
                {event.date.month.label}
              </Box>
            </Box>
            <Box textStyle='body1' fontWeight={700} mx='auto' height='28px' mt='-0.4rem'>
              {event.date.dayOfMonth}
            </Box>
            <Box textStyle='tagSm' fontWeight={700} mx='auto' height='20px'>
              {event.date.year}
            </Box>
          </Stack>

          <Stack my='auto' gap={0}>
            <Box fontWeight={600} color='typography.100' lineClamp={1}>
              {event.title}
            </Box>
            <Box textStyle='body4' fontWeight={400} color='typography.300'>
              {event.date.time.default}
            </Box>
          </Stack>
        </Flex>

        <MeetingFooter
          members={event.members}
          startsIn={event.date.relativeShort}
          relativeSeconds={event.date.relativeSeconds}
        />
      </Stack>
    </NextLink>
  )
}
