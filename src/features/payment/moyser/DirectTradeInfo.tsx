import { RiyalNewSignIcon } from '@/components/EbanaIcons'
import { useAuth } from '@/context/new-auth'
import { graphql } from '@/graphql'
import { useEbanaLocale } from '@/hooks/useEbanaTranslations'
import { Box, Flex, Stack } from '@chakra-ui/react'
import { useQuery } from 'urql'

const directTradeFeeQuery = graphql(`
    query MyQuery($principalId: String, $representation: Boolean!, $id: ID!) {
        me @skip(if: $representation) {
            directTrade(id: $id) {
                sharePrice {
                    formatted
                    value
                }
                shareCount {
                    formatted
                    value
                }
                company {
                    tradeName
                    directTradeFee {
                        formatted
                        value
                    }
                }
            }
        }
        as(principalId: $principalId) @include(if: $representation) {
            directTrade(id: $id) {
                sharePrice {
                    formatted
                    value
                }
                shareCount {
                    formatted
                    value
                }
                company {
                    tradeName
                    directTradeFee {
                        formatted
                        value
                    }
                }
            }
        }
    }
`)

export function DirectTradeInfo({ id, totalAmount }) {
  const { t } = useEbanaLocale()

  const { representation, principalId } = useAuth()

  const [{ data }] = useQuery({
    query: directTradeFeeQuery,
    variables: {
      id,
      representation,
      principalId,
    },
  })

  const directTrade = data?.me?.directTrade || data?.as?.directTrade

  const companyTradeName = directTrade?.company?.tradeName
  const directTradeFee = directTrade?.company?.directTradeFee
  const shareCount = directTrade?.shareCount?.formatted
  const sharePrice = directTrade?.sharePrice?.formatted

  return (
    <>
      <Box
        mt='3rem'
        borderWidth='1px'
        background='background.300'
        borderRadius='14px'
        borderColor='stroke.100'
        p='1.875rem'>
        <Stack>
          <Flex justifyContent='space-between'>
            <Box textStyle='body2' fontWeight={600}>
              {t('stock_request_summary')}
            </Box>
            <Box my='auto' color='typography.200'>
              #{id}
            </Box>
          </Flex>
          <Flex mt='1.25rem' justifyContent='space-between'>
            <Box color='typography.200'>{t('company_name')}</Box>
            <Box fontWeight={600}>{companyTradeName}</Box>
          </Flex>
          <Flex mt='1.25rem' justifyContent='space-between'>
            <Box color='typography.200'>{t('number-of-shares')}</Box>
            <Box fontWeight={500}>{shareCount}</Box>
          </Flex>
          {sharePrice != null && (
            <Flex justifyContent='space-between'>
              <Box color='typography.200'>{t('share_price')}</Box>
              <Box width='fit-content' dir='ltr' fontWeight={500}>
                <RiyalNewSignIcon />
                {sharePrice.replace('SAR', '').replace('﷼', '')}
              </Box>
            </Flex>
          )}
        </Stack>
      </Box>
      <Box
        mb='2rem'
        mt='1rem'
        borderWidth='1px'
        background='background.300'
        borderRadius='14px'
        borderColor='stroke.100'
        p='1.875rem'>
        <Stack>
          <Box textStyle='body2' fontWeight={600}>
            {t('sell_stock_fees')}
          </Box>
          <Flex mt='1.25rem' justifyContent='space-between'>
            <Box color='typography.200' my='auto'>
              {t('platform_fees')}
            </Box>
            <Box width='fit-content' dir='ltr' fontWeight={500}>
              <RiyalNewSignIcon />
              {directTradeFee?.formatted.replace('SAR', '').replace('﷼', '')}
            </Box>
          </Flex>
          <Flex justifyContent='space-between'>
            <Box color='typography.200' my='auto'>
              {t('includes_vat')}
            </Box>
            <Box width='fit-content' dir='ltr' fontWeight={500}>
              <RiyalNewSignIcon /> {(totalAmount - (directTradeFee?.value ?? 0)).toFixed(2)}
            </Box>
          </Flex>
          <Box mt='5px' height='1px' backgroundColor='stroke.100' />
          <Flex justifyContent='space-between'>
            <Box fontWeight={600} textStyle='body2'>
              {t('total')}
            </Box>
            <Box dir='ltr' fontWeight={600} textStyle='body2'>
              <RiyalNewSignIcon /> {totalAmount}
            </Box>
          </Flex>
        </Stack>
      </Box>
    </>
  )
}
